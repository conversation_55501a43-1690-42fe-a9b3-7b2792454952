{"version": 1, "defects": {"EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDataManipulationQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAdvancedTransformationsQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDocumentationExample": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testStringFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDateFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpecialCharacters": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithHyphen": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpace": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldWithSpecialCharacters": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldBothPartsWithSpecialCharacters": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testDateFormatWithSpecialFieldReference": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testComplexNestedField": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldStartingWithNumber": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithDollarSign": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithMixedSpecialCharacters": 7, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testTop": 7, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testAggregationInQuery": 7, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testTopAndValuesInQuery": 7, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testSpatialAggregationsInQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testComprehensiveEmployeeStatistics": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testSalesAnalysisWithTopAndValues": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testGeographicAnalysisWithSpatialFunctions": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testPerformanceMetricsAnalysis": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testComplexQueryWithInlineFunctions": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AvgTest::testAvgInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AvgTest::testAvgWithInlineFunctionInQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\CountDistinctTest::testCountDistinctInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\CountDistinctTest::testCountDistinctWithPrecisionInQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianAbsoluteDeviationTest::testMedianAbsoluteDeviationInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianAbsoluteDeviationTest::testMedianAbsoluteDeviationGroupedByDepartment": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianTest::testMedianInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianTest::testMedianGroupedByDepartment": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testMultiplePercentilesInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testPercentileGroupedByDepartment": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggInCompleteQueryFromDocumentation": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggGroupedByRegion": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggWithFiltering": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggWithMultipleGeometryTypes": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggInCompleteQueryFromDocumentation": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggGroupedByRegion": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggWithFiltering": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggWithMultipleGeometryTypes": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggCombinedWithStCentroidAgg": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevWithInlineFunctionInQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevGroupedByDepartment": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumWithInlineFunctionInQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumGroupedByDepartment": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testBasicTop": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopWithDescOrder": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopWithDifferentLimits": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopInCompleteQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopWithDifferentFields": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopGroupedByDepartment": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesInCompleteQueryFromDocumentation": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesWithMvSort": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesSimpleUsage": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesWithMultipleFields": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgInCompleteQueryFromDocumentation": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgGroupedByDepartment": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgWithRound": 7, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testAllNewAggregationFunctionsWithCorrectEvalSyntax": 7, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testAggregationFunctionsWithOptionalParameters": 7, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testCompleteStatisticalAnalysisQuery": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedTopWithValidArguments": 8, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedTopWithInvalidOrder": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedTopWithInvalidLimit": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedPercentileWithNegativePercentile": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedCountDistinctWithValidArguments": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedCountDistinctWithInvalidPrecision": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedFunctionsWithComplexFieldExpressions": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testArgumentTypeIntrospection": 7, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedCountDistinctWithNegativePrecision": 7}, "times": {"EsqlPhp\\Tests\\Unit\\Clauses\\FromClauseTest::testFromClauseToString": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testToStringWithNoSorts": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddSingleSortAscending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddSingleSortDescending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddMultipleSorts": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testInvalidDirectionDefaultsToAscending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testCaseInsensitiveDirection": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testSimpleCondition": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testMultipleAndConditions": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testOrCondition": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testComplexConditions": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testValueFormatting": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testSimpleQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDataManipulationQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAdvancedTransformationsQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testComposedConditionsQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDocumentationExample": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testMathFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testStringFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDateFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testSimpleFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithUnderscore": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceStartingWithAt": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithDot": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpecialCharacters": 0.002, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithHyphen": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpace": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldWithSpecialCharacters": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldBothPartsWithSpecialCharacters": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceAlreadyWithBackticks": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldAlreadyWithBackticks": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testStringLiteralNotFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testStringLiteralWithSpecialCharacters": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testDateFormatWithFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testDateFormatWithSpecialFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testComplexNestedField": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldStartingWithNumber": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithDollarSign": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithMultipleDots": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithMixedSpecialCharacters": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testCountDistinct": 0.001, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testMedian": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testMedianAbsoluteDeviation": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testPercentile": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testStCentroidAgg": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testStExtentAgg": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testStdDev": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testTop": 0.002, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testValues": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testWeightedAvg": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testAggregationInQuery": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testTopAndValuesInQuery": 0, "EsqlPhp\\QueryBuilder\\Tests\\Unit\\AggregationFunctionsTest::testSpatialAggregationsInQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testComprehensiveEmployeeStatistics": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testSalesAnalysisWithTopAndValues": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testGeographicAnalysisWithSpatialFunctions": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testPerformanceMetricsAnalysis": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AggregationIntegrationTest::testComplexQueryWithInlineFunctions": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AvgTest::testBasicAvg": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AvgTest::testAvgWithInlineFunction": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AvgTest::testAvgInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\AvgTest::testAvgWithInlineFunctionInQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\CountDistinctTest::testBasicCountDistinct": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\CountDistinctTest::testCountDistinctWithPrecision": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\CountDistinctTest::testCountDistinctInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\CountDistinctTest::testCountDistinctWithPrecisionInQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\CountDistinctTest::testCountDistinctWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianAbsoluteDeviationTest::testBasicMedianAbsoluteDeviation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianAbsoluteDeviationTest::testMedianAbsoluteDeviationWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianAbsoluteDeviationTest::testMedianAbsoluteDeviationInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianAbsoluteDeviationTest::testMedianAbsoluteDeviationWithInlineFunction": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianAbsoluteDeviationTest::testMedianAbsoluteDeviationGroupedByDepartment": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianTest::testBasicMedian": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianTest::testMedianWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianTest::testMedianInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianTest::testMedianWithInlineFunction": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\MedianTest::testMedianGroupedByDepartment": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testBasicPercentile": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testPercentileWithDifferentValues": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testPercentileWithDecimalValues": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testMultiplePercentilesInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testPercentileWithInlineFunction": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\PercentileTest::testPercentileGroupedByDepartment": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testBasicStCentroidAgg": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggInCompleteQueryFromDocumentation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggGroupedByRegion": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggWithFiltering": 0.002, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StCentroidAggTest::testStCentroidAggWithMultipleGeometryTypes": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testBasicStExtentAgg": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggInCompleteQueryFromDocumentation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggGroupedByRegion": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggWithFiltering": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggWithMultipleGeometryTypes": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StExtentAggTest::testStExtentAggCombinedWithStCentroidAgg": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testBasicStdDev": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevWithInlineFunction": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevWithInlineFunctionInQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\StdDevTest::testStdDevGroupedByDepartment": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testBasicSum": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumWithInlineFunction": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumWithInlineFunctionInQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\SumTest::testSumGroupedByDepartment": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testBasicTop": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopWithDescOrder": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopWithDifferentLimits": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopInCompleteQuery": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TopTest::testTopGroupedByDepartment": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testBasicValues": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesInCompleteQueryFromDocumentation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesWithMvSort": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesSimpleUsage": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\ValuesTest::testValuesWithMultipleFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testBasicWeightedAvg": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgWithNumericWeight": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgWithIntegerWeight": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgInCompleteQueryFromDocumentation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgWithDifferentFields": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgGroupedByDepartment": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\WeightedAvgTest::testWeightedAvgWithRound": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\EvalClauseTest::testSingleExpressionWithAlias": 0.001, "EsqlPhp\\Tests\\Unit\\Clauses\\EvalClauseTest::testSingleExpressionWithoutAlias": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\EvalClauseTest::testMultipleExpressions": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\EvalClauseTest::testMixedExpressionsWithAndWithoutAlias": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\EvalClauseTest::testEmptyEvalClause": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\EvalClauseTest::testStringExpressionWithAlias": 0, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testAllNewAggregationFunctionsWithCorrectEvalSyntax": 0, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testSpatialAggregationFunctions": 0, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testAggregationFunctionsWithOptionalParameters": 0, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testAggregationFunctionsWithInlineExpressions": 0, "EsqlPhp\\Tests\\Unit\\NewAggregationFunctionsTest::testCompleteStatisticalAnalysisQuery": 0.003, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testFieldArgumentFormatting": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testStringArgumentFormatting": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testNumericArgumentFormatting": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testEnumArgumentValidation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testNumericArgumentValidation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testNumericArgumentMinMaxValidation": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testOptionalArgumentWithValue": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testOptionalArgumentWithoutValue": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testStringArgumentWithConstraints": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testStringArgumentWithPatternConstraint": 0, "EsqlPhp\\Tests\\Unit\\Functions\\ArgumentTypes\\ArgumentTypesTest::testStringArgumentWithLengthConstraints": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedTopWithValidArguments": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedTopWithInvalidOrder": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedTopWithInvalidLimit": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedTopWithNegativeLimit": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedPercentileWithValidArguments": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedPercentileWithInvalidPercentile": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedPercentileWithNegativePercentile": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedCountDistinctWithValidArguments": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedCountDistinctWithInvalidPrecision": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedCountDistinctWithNegativePrecision": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testTypedFunctionsWithComplexFieldExpressions": 0, "EsqlPhp\\Tests\\Unit\\Functions\\Aggregation\\TypedFunctionsTest::testArgumentTypeIntrospection": 0}}