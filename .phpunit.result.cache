{"version": 1, "defects": {"EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDataManipulationQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAdvancedTransformationsQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDocumentationExample": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testStringFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDateFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpecialCharacters": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithHyphen": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpace": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldWithSpecialCharacters": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldBothPartsWithSpecialCharacters": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testDateFormatWithSpecialFieldReference": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testComplexNestedField": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldStartingWithNumber": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithDollarSign": 7, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithMixedSpecialCharacters": 7}, "times": {"EsqlPhp\\Tests\\Unit\\Clauses\\FromClauseTest::testFromClauseToString": 0.001, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testToStringWithNoSorts": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddSingleSortAscending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddSingleSortDescending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddMultipleSorts": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testInvalidDirectionDefaultsToAscending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testCaseInsensitiveDirection": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testSimpleCondition": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testMultipleAndConditions": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testOrCondition": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testComplexConditions": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testValueFormatting": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testSimpleQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDataManipulationQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAdvancedTransformationsQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testComposedConditionsQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDocumentationExample": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testMathFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testStringFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDateFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testSimpleFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithUnderscore": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceStartingWithAt": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithDot": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpecialCharacters": 0.002, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithHyphen": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceWithSpace": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldWithSpecialCharacters": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldBothPartsWithSpecialCharacters": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldReferenceAlreadyWithBackticks": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testNestedFieldAlreadyWithBackticks": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testStringLiteralNotFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testStringLiteralWithSpecialCharacters": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testDateFormatWithFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testDateFormatWithSpecialFieldReference": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testComplexNestedField": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldStartingWithNumber": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithDollarSign": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithMultipleDots": 0, "EsqlPhp\\Tests\\Unit\\Functions\\FieldReferenceTest::testFieldWithMixedSpecialCharacters": 0}}