# ES|QL Query Builder for PHP

Une librairie PHP complète et modulaire pour construire des requêtes Elasticsearch ES|QL de manière fluide et sécurisée. Cette librairie implémente toutes les fonctionnalités de la spécification ES|QL officielle d'Elasticsearch.

## Installation

```bash
composer require esql-php/query-builder
```

## Exemples d'utilisation

### Requête simple

```php
<?php
use EsqlPhp\QueryBuilder\QueryBuilder;

$query = (new QueryBuilder())
    ->from('users')
    ->where('age', '>', 25)
    ->where('status', '=', 'active')
    ->orderBy('lastname', 'ASC')
    ->limit(10);

echo $query->toEsql();
```

### Requête avec agrégations et groupement

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->eval(QueryBuilder::count('*'), 'employee_count')
    ->where('hire_date', '>', '2020-01-01')
    ->orderBy('avg_salary', 'DESC');
```

### Requête avec manipulation de données

```php
$query = (new QueryBuilder())
    ->from('logs')
    ->eval(QueryBuilder::dateFormat('timestamp', 'yyyy-MM-dd'), 'date')
    ->eval(QueryBuilder::concat(['user', '\'@\'', 'domain']), 'email')
    ->where('level', '=', 'ERROR')
    ->drop(['raw_message'])
    ->rename(['timestamp' => 'original_timestamp'])
    ->limit(20);
```

### Requête avec transformations avancées

```php
$query = (new QueryBuilder())
    ->from('products')
    ->eval(QueryBuilder::round('price * 1.2', 2), 'price_with_tax')
    ->where('stock', '>', 0)
    ->dissolve(['tags'])
    ->melt(['us_price', 'eu_price', 'asia_price'], 'region', 'regional_price')
    ->orderBy('name')
    ->keep(['product_id', 'name', 'category', 'price_with_tax', 'region', 'regional_price']);
```

## Fonctionnalités

### Clauses de base

- `from(string $index)`: Définit l'index source de la requête
- `where(string $field, string $operator, $value)`: Ajoute une condition avec opérateur ET
- `orWhere(string $field, string $operator, $value)`: Ajoute une condition avec opérateur OU
- `limit(int $limit, ?int $offset = null)`: Limite le nombre de résultats (avec pagination optionnelle)
- `orderBy(string $field, string $direction = 'ASC')`: Ajoute un critère de tri
- `groupBy(array $fields)`: Regroupe les résultats par champs

### Commandes ES|QL

- `eval(string $expression, ?string $alias = null)`: Évalue une expression et assigne le résultat à un alias
- `keep(array $fields)`: Conserve uniquement certains champs dans les résultats
- `drop(array $fields)`: Supprime des champs des résultats
- `rename(array $renames)`: Renomme des champs
- `enrich(string $policyName, array $fields = [])`: Applique une politique d'enrichissement
- `melt(array $fields, ?string $asField = null, ?string $valuesField = null)`: Transforme les données en format long
- `dissolve(array $fields)`: Déplie les tableaux en lignes multiples

### Fonctions mathématiques

- `QueryBuilder::abs($value)`: Valeur absolue
- `QueryBuilder::ceil($value)`: Arrondi supérieur
- `QueryBuilder::floor($value)`: Arrondi inférieur
- `QueryBuilder::round($value, ?int $decimals = null)`: Arrondi avec précision décimale

### Fonctions de chaîne

- `QueryBuilder::concat(array $strings)`: Concaténation de chaînes
- `QueryBuilder::length($string)`: Longueur d'une chaîne
- `QueryBuilder::substring($string, int $start, ?int $length = null)`: Extraction d'une sous-chaîne
- `QueryBuilder::lowercase($string)`: Conversion en minuscules
- `QueryBuilder::uppercase($string)`: Conversion en majuscules

### Fonctions de date

- `QueryBuilder::now()`: Date et heure actuelles
- `QueryBuilder::dateFormat($dateTime, string $format)`: Formatage d'une date
- `QueryBuilder::dateTrunc(string $unit, $dateTime)`: Troncature d'une date

### Fonctions d'agrégation

- `QueryBuilder::count($field = '*')`: Compte le nombre d'éléments
- `QueryBuilder::sum(string $field)`: Somme des valeurs
- `QueryBuilder::avg(string $field)`: Moyenne des valeurs
- `QueryBuilder::min(string $field)`: Valeur minimale
- `QueryBuilder::max(string $field)`: Valeur maximale

## Architecture

La librairie est conçue de manière modulaire avec les composants suivants :

- **QueryBuilder**: Classe principale pour construire des requêtes
- **Clauses**: Implémentation des différentes clauses ES|QL
- **Commands**: Implémentation des commandes ES|QL
- **Functions**: Implémentation des fonctions ES|QL (Math, String, Date, Aggregation)

## Développement

### Installation des dépendances

```bash
composer install
```

### Exécution des tests

```bash
composer test
```

### Vérification du style de code

```bash
composer cs-check
```

### Correction automatique du style de code

```bash
composer cs-fix
```

## Références

- [Documentation officielle ES|QL](https://www.elastic.co/docs/reference/query-languages/esql)
- [Fonctions et opérateurs ES|QL](https://www.elastic.co/docs/reference/query-languages/esql/esql-functions-operators)
- [Commandes ES|QL](https://www.elastic.co/docs/reference/query-languages/esql/esql-commands)
- [Exemples ES|QL](https://www.elastic.co/docs/reference/query-languages/esql/esql-examples)

## Licence

MIT
