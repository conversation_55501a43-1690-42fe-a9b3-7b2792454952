# Fonctions d'agrégation ESQL

Ce document présente toutes les fonctions d'agrégation disponibles dans la bibliothèque ESQL PHP, basées sur la [documentation officielle Elasticsearch](https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions).

## Fonctions d'agrégation disponibles

### AVG
Calcule la moyenne d'une expression numérique.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::avg('salary'), 'avg_salary');
```

### COUNT
Compte le nombre de valeurs.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::count('*'), 'total_count');
```

### COUNT_DISTINCT
Retourne le nombre approximatif de valeurs distinctes.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::countDistinct('languages'), 'unique_languages')
    ->eval(QueryBuilder::countDistinct('department', 1000), 'unique_departments_precise');
```

### MAX
Trouve la valeur maximale.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::max('salary'), 'max_salary');
```

### MEDIAN
Calcule la médiane d'une expression numérique.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::median('salary'), 'median_salary');
```

### MEDIAN_ABSOLUTE_DEVIATION
Calcule la déviation absolue médiane.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::medianAbsoluteDeviation('salary'), 'mad_salary');
```

### MIN
Trouve la valeur minimale.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::min('salary'), 'min_salary');
```

### PERCENTILE
Calcule la valeur à laquelle un certain pourcentage de valeurs observées se produit.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::percentile('salary', 95), 'p95_salary')
    ->eval(QueryBuilder::percentile('salary', 50), 'median_salary');
```

### ST_CENTROID_AGG
Calcule le centroïde spatial sur un champ avec type géométrique.

```php
$query = (new QueryBuilder())
    ->from('airports')
    ->where('country', '=', 'France')
    ->eval(QueryBuilder::stCentroidAgg('location'), 'centroid');
```

### ST_EXTENT_AGG
Calcule l'étendue spatiale sur un champ avec type géométrique.

```php
$query = (new QueryBuilder())
    ->from('airports')
    ->where('country', '=', 'France')
    ->eval(QueryBuilder::stExtentAgg('location'), 'extent');
```

### STD_DEV
Calcule l'écart-type d'un champ numérique.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::stdDev('height'), 'height_stddev');
```

### SUM
Calcule la somme d'une expression numérique.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::sum('salary'), 'total_salary');
```

### TOP
Collecte les valeurs supérieures pour un champ.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::top('salary', 3, 'desc'), 'top_salaries');
```

### VALUES
Retourne les valeurs uniques sous forme de champ multivalué (en préversion technique).

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::values('job_title'), 'job_titles');
```

### WEIGHTED_AVG
Calcule la moyenne pondérée d'une expression numérique.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::weightedAvg('salary', 'experience'), 'weighted_avg_salary');
```

## Exemples d'utilisation avancée

### Analyse statistique complète

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::count('*'), 'employee_count')
    ->eval(QueryBuilder::min('salary'), 'min_salary')
    ->eval(QueryBuilder::max('salary'), 'max_salary')
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->eval(QueryBuilder::median('salary'), 'median_salary')
    ->eval(QueryBuilder::percentile('salary', 25), 'q1_salary')
    ->eval(QueryBuilder::percentile('salary', 75), 'q3_salary')
    ->eval(QueryBuilder::stdDev('salary'), 'salary_stddev')
    ->orderBy('avg_salary', 'DESC');
```

### Analyse de performance avec percentiles

```php
$query = (new QueryBuilder())
    ->from('api_requests')
    ->where('status_code', '=', 200)
    ->groupBy(['endpoint'])
    ->eval(QueryBuilder::count('*'), 'request_count')
    ->eval(QueryBuilder::percentile('response_time', 50), 'p50_response')
    ->eval(QueryBuilder::percentile('response_time', 95), 'p95_response')
    ->eval(QueryBuilder::percentile('response_time', 99), 'p99_response')
    ->eval(QueryBuilder::stdDev('response_time'), 'response_stddev')
    ->orderBy('p95_response', 'DESC');
```

### Analyse géographique

```php
$query = (new QueryBuilder())
    ->from('stores')
    ->groupBy(['city'])
    ->eval(QueryBuilder::count('*'), 'store_count')
    ->eval(QueryBuilder::stCentroidAgg('location'), 'city_center')
    ->eval(QueryBuilder::stExtentAgg('location'), 'coverage_area')
    ->eval(QueryBuilder::avg('revenue'), 'avg_revenue')
    ->orderBy('store_count', 'DESC');
```

## Fonctions avec expressions inline

Toutes les fonctions d'agrégation supportent les expressions inline comme `MV_MAX()`, `MV_MIN()`, etc.

```php
$query = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::avg('MV_MAX(salary_change)'), 'avg_max_salary_change')
    ->eval(QueryBuilder::sum('MV_MAX(salary_change)'), 'total_salary_changes')
    ->eval(QueryBuilder::stdDev('MV_MAX(salary_change)'), 'stddev_salary_change');
```

## Notes importantes

- **COUNT_DISTINCT** : Les comptes sont approximatifs par défaut. Utilisez le paramètre `precision` pour plus de précision.
- **PERCENTILE** : Les percentiles sont (généralement) approximatifs pour des raisons de performance.
- **VALUES** : Cette fonction est en préversion technique et peut utiliser beaucoup de mémoire.
- **Fonctions spatiales** : `ST_CENTROID_AGG` et `ST_EXTENT_AGG` nécessitent des champs de type géométrique.

Pour plus d'informations, consultez la [documentation officielle ESQL](https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions).
