# Système de fonctions typées ESQL

Ce document présente le nouveau système de fonctions typées qui apporte une validation automatique des arguments et un formatage intelligent pour les fonctions ESQL.

## Avantages du système typé

### 1. Validation automatique des arguments
- **Validation des types** : Vérification que les arguments sont du bon type (string, numeric, etc.)
- **Validation des contraintes** : Vérification des plages de valeurs, valeurs autorisées, etc.
- **Messages d'erreur clairs** : Indication précise de ce qui ne va pas

### 2. Formatage automatique et intelligent
- **Quotage automatique** : Les chaînes littérales sont automatiquement quotées
- **Préservation des références** : Les références de champs ne sont pas quotées
- **Normalisation des valeurs** : Les énumérations sont automatiquement normalisées

### 3. Documentation intégrée
- **Introspection des types** : Possibilité d'examiner les types d'arguments attendus
- **Contraintes visibles** : Les contraintes sont documentées dans le code

## Types d'arguments disponibles

### FieldArgument
Représente une référence de champ ou une expression qui ne doit pas être quotée.

```php
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\FieldArgument;

// Référence de champ simple
$field1 = new FieldArgument('salary');
echo $field1->format(); // salary

// Référence de champ imbriqué
$field2 = new FieldArgument('employee.department');
echo $field2->format(); // employee.department

// Appel de fonction
$field3 = new FieldArgument('MV_MAX(scores)');
echo $field3->format(); // MV_MAX(scores)

// Expression mathématique
$field4 = new FieldArgument('salary * 1.2');
echo $field4->format(); // salary * 1.2
```

### StringArgument
Représente une chaîne littérale qui doit être quotée.

```php
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\StringArgument;

// Chaîne simple
$str1 = new StringArgument('hello');
echo $str1->format(); // 'hello'

// Chaîne avec apostrophe
$str2 = new StringArgument("it's working");
echo $str2->format(); // 'it''s working'

// Avec contraintes de valeurs autorisées
$str3 = new StringArgument('option1', ['allowed_values' => ['option1', 'option2']]);

// Avec contrainte de pattern
$str4 = new StringArgument('2024-01-01', ['pattern' => '/^\d{4}-\d{2}-\d{2}$/']);

// Avec contraintes de longueur
$str5 = new StringArgument('hello', ['min_length' => 3, 'max_length' => 10]);
```

### NumericArgument
Représente un argument numérique avec validation des contraintes.

```php
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\NumericArgument;

// Nombre simple
$num1 = new NumericArgument(42);
echo $num1->format(); // 42

// Avec contrainte positive
$num2 = new NumericArgument(5, ['positive' => true]);

// Avec contrainte d'entier
$num3 = new NumericArgument(10, ['integer' => true]);

// Avec plage de valeurs
$num4 = new NumericArgument(50, ['min' => 0, 'max' => 100]);

// Avec contrainte non-négative
$num5 = new NumericArgument(0, ['non_negative' => true]);
```

### EnumArgument
Représente un argument avec un ensemble fixe de valeurs autorisées.

```php
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\EnumArgument;

// Énumération sensible à la casse
$enum1 = new EnumArgument('asc', ['asc', 'desc']);
echo $enum1->format(); // 'asc'

// Énumération insensible à la casse
$enum2 = new EnumArgument('ASC', ['asc', 'desc'], false);
echo $enum2->format(); // 'asc' (normalisé)

// Erreur pour valeur invalide
try {
    $enum3 = new EnumArgument('invalid', ['asc', 'desc']);
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // Invalid value "invalid". Allowed values are: asc, desc
}
```

### OptionalArgument
Wrapper pour rendre un argument optionnel avec une valeur par défaut.

```php
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\EnumArgument;

// Argument optionnel avec valeur par défaut
$orderEnum = new EnumArgument('asc', ['asc', 'desc']);
$optional = new OptionalArgument($orderEnum, 'asc');

// Vérifier si une valeur est fournie
if ($optional->hasValue()) {
    echo $optional->format();
} else {
    echo "Utilisation de la valeur par défaut";
}
```

## Création de fonctions typées

### Étendre TypedAbstractFunction

```php
use EsqlPhp\QueryBuilder\Functions\TypedAbstractFunction;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\FieldArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\NumericArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\EnumArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument;

class TypedTop extends TypedAbstractFunction
{
    public function __construct($field, int $limit, string $order = 'asc')
    {
        parent::__construct('top', [$field, $limit, $order]);
    }

    protected function defineArgumentTypes(): void
    {
        // Argument 1: Champ (obligatoire)
        $this->addArgumentType(new FieldArgument(''));

        // Argument 2: Limite (entier positif, obligatoire)
        $this->addArgumentType(new NumericArgument(0, [
            'integer' => true,
            'positive' => true,
            'min' => 1
        ]));

        // Argument 3: Ordre (énumération, optionnel avec défaut 'asc')
        $orderEnum = new EnumArgument('asc', ['asc', 'desc'], false);
        $this->addArgumentType(new OptionalArgument($orderEnum, 'asc'));
    }
}
```

## Exemples d'utilisation

### Fonction TOP avec validation

```php
// Utilisation correcte
$top1 = new TypedTop('salary', 5, 'desc');
echo $top1->toString(); // top(salary, 5, 'desc')

// Normalisation automatique de la casse
$top2 = new TypedTop('salary', 3, 'ASC');
echo $top2->toString(); // top(salary, 3, 'asc')

// Valeur par défaut
$top3 = new TypedTop('salary', 10);
echo $top3->toString(); // top(salary, 10, 'asc')

// Erreurs automatiquement détectées
try {
    $topInvalid = new TypedTop('salary', -1); // Erreur: limite négative
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // Value must be positive
}

try {
    $topInvalid = new TypedTop('salary', 5, 'invalid'); // Erreur: ordre invalide
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // Invalid value "invalid". Allowed values are: asc, desc
}
```

### Fonction PERCENTILE avec validation de plage

```php
// Utilisation correcte
$p1 = new TypedPercentile('salary', 95);
echo $p1->toString(); // percentile(salary, 95)

$p2 = new TypedPercentile('response_time', 99.9);
echo $p2->toString(); // percentile(response_time, 99.9)

// Erreurs automatiquement détectées
try {
    $pInvalid = new TypedPercentile('salary', 101); // Erreur: > 100
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // Value must be at most 100
}

try {
    $pInvalid = new TypedPercentile('salary', -5); // Erreur: négatif
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // Value must be non-negative
}
```

## Migration depuis les fonctions non-typées

Les fonctions typées sont conçues pour être compatibles avec l'API existante :

```php
// Ancienne façon (toujours supportée)
$top = new Top('salary', 3, 'desc');

// Nouvelle façon (avec validation)
$typedTop = new TypedTop('salary', 3, 'desc');

// Les deux produisent le même résultat ESQL
echo $top->toString();      // top(salary, 3, 'desc')
echo $typedTop->toString(); // top(salary, 3, 'desc')
```

## Introspection des types

```php
$top = new TypedTop('salary', 3, 'desc');
$argumentTypes = $top->getArgumentTypes();

foreach ($argumentTypes as $index => $type) {
    echo "Argument $index: " . get_class($type) . "\n";
    
    if ($type instanceof OptionalArgument) {
        $wrappedType = $type->getWrappedType();
        echo "  Type encapsulé: " . get_class($wrappedType) . "\n";
        
        if ($wrappedType instanceof EnumArgument) {
            echo "  Valeurs autorisées: " . implode(', ', $wrappedType->getAllowedValues()) . "\n";
        }
    }
}
```

Ce système apporte une robustesse et une sécurité accrues lors de la construction de requêtes ESQL, tout en maintenant la compatibilité avec l'API existante.
