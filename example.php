<?php

require_once __DIR__ . '/vendor/autoload.php';

use EsqlPhp\QueryBuilder\QueryBuilder;

// ======= Exemple 1: Requête simple =======
echo "\n\n===== Exemple 1: Requête simple =====\n";

$query1 = (new QueryBuilder())
    ->from('users')
    ->where('age', '>', 25)
    ->where('status', '=', 'active')
    ->orderBy('lastname', 'ASC')
    ->limit(10);

echo $query1->toEsql() . "\n";

// ======= Exemple 2: Agrégations et Groupement =======
echo "\n\n===== Exemple 2: Agrégations et Groupement =====\n";

$query2 = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->eval(QueryBuilder::count('*'), 'employee_count')
    ->where('hire_date', '>', '2020-01-01')
    ->orderBy('avg_salary', 'DESC');

echo $query2->toEsql() . "\n";

// ======= Exemple 3: Manipulation de données =======
echo "\n\n===== Exemple 3: Manipulation de données =====\n";

$query3 = (new QueryBuilder())
    ->from('logs')
    ->eval(QueryBuilder::dateFormat('timestamp', 'yyyy-MM-dd'), 'date')
    ->eval(QueryBuilder::concat(['user', '\'@\'', 'domain']), 'email')
    ->where('level', '=', 'ERROR')
    ->drop(['raw_message'])
    ->rename(['timestamp' => 'original_timestamp'])
    ->limit(20);

echo $query3->toEsql() . "\n";

// ======= Exemple 4: Transformations avancées =======
echo "\n\n===== Exemple 4: Transformations avancées =====\n";

$query4 = (new QueryBuilder())
    ->from('products')
    ->eval(QueryBuilder::round('price * 1.2', 2), 'price_with_tax')
    ->where('stock', '>', 0)
    ->dissolve(['tags'])
    ->melt(['us_price', 'eu_price', 'asia_price'], 'region', 'regional_price')
    ->orderBy('name')
    ->keep(['product_id', 'name', 'category', 'price_with_tax', 'region', 'regional_price']);

echo $query4->toEsql() . "\n";

// ======= Exemple 5: Conditions composées =======
echo "\n\n===== Exemple 5: Conditions composées =====\n";

$query5 = (new QueryBuilder())
    ->from('orders')
    ->where('status', '=', 'PENDING')
    ->where('total', '>', 100)
    ->orWhere('customer_id', 'IN', [1001, 1002, 1003])
    ->orderBy('total', 'DESC')
    ->limit(5);

echo $query5->toEsql() . "\n";

// Basé sur un exemple de la documentation ES|QL
// https://www.elastic.co/docs/reference/query-languages/esql/esql-examples
echo "\n\n===== Exemple de la documentation ES|QL =====\n";

// Filter e-commerce orders based on the category field from a nested array of products
$query6 = (new QueryBuilder())
    ->from('kibana_sample_data_ecommerce')
    ->where('products.category', '=', 'Men\'s Clothing')
    ->dissolve(['products'])
    ->where('products.category', '=', 'Men\'s Clothing')
    ->keep(['order_id', 'customer_id', 'products.product_id', 'products.category']);
    
echo $query6->toEsql() . "\n";

