<?php

require_once __DIR__ . '/../vendor/autoload.php';

use EsqlPhp\QueryBuilder\QueryBuilder;

echo "=== Exemples des fonctions d'agrégation ESQL ===\n\n";

// ======= Exemple 1: Analyse des employés avec COUNT_DISTINCT et MEDIAN =======
echo "1. Analyse des employés par département:\n";
$query1 = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::count('*'), 'total_employees')
    ->eval(QueryBuilder::countDistinct('job_title'), 'unique_roles')
    ->eval(QueryBuilder::median('salary'), 'median_salary')
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->orderBy('median_salary', 'DESC');

echo $query1->toEsql() . "\n\n";

// ======= Exemple 2: Analyse de performance avec PERCENTILE et STD_DEV =======
echo "2. Analyse de performance des API:\n";
$query2 = (new QueryBuilder())
    ->from('api_logs')
    ->where('status_code', '=', 200)
    ->groupBy(['endpoint'])
    ->eval(QueryBuilder::count('*'), 'request_count')
    ->eval(QueryBuilder::percentile('response_time', 50), 'p50_response')
    ->eval(QueryBuilder::percentile('response_time', 95), 'p95_response')
    ->eval(QueryBuilder::percentile('response_time', 99), 'p99_response')
    ->eval(QueryBuilder::stdDev('response_time'), 'response_stddev')
    ->eval(QueryBuilder::medianAbsoluteDeviation('response_time'), 'response_mad')
    ->orderBy('p95_response', 'DESC');

echo $query2->toEsql() . "\n\n";

// ======= Exemple 3: Analyse des ventes avec TOP et VALUES =======
echo "3. Analyse des ventes avec top deals et catégories:\n";
$query3 = (new QueryBuilder())
    ->from('sales')
    ->groupBy(['region'])
    ->eval(QueryBuilder::sum('amount'), 'total_sales')
    ->eval(QueryBuilder::top('amount', 3, 'desc'), 'top_3_deals')
    ->eval(QueryBuilder::values('product_category'), 'categories_sold')
    ->eval(QueryBuilder::countDistinct('customer_id'), 'unique_customers')
    ->eval(QueryBuilder::weightedAvg('amount', 'quantity'), 'weighted_avg_amount')
    ->orderBy('total_sales', 'DESC');

echo $query3->toEsql() . "\n\n";

// ======= Exemple 4: Analyse géographique avec fonctions spatiales =======
echo "4. Analyse géographique des aéroports:\n";
$query4 = (new QueryBuilder())
    ->from('airports')
    ->where('country', '=', 'France')
    ->eval(QueryBuilder::count('*'), 'airport_count')
    ->eval(QueryBuilder::stCentroidAgg('location'), 'geographic_center')
    ->eval(QueryBuilder::stExtentAgg('location'), 'coverage_area');

echo $query4->toEsql() . "\n\n";

// ======= Exemple 5: Analyse complexe avec fonctions inline =======
echo "5. Analyse complexe avec fonctions MV_MAX:\n";
$query5 = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::avg('MV_MAX(salary_change)'), 'avg_max_salary_change')
    ->eval(QueryBuilder::sum('MV_MAX(salary_change)'), 'total_salary_changes')
    ->eval(QueryBuilder::stdDev('MV_MAX(salary_change)'), 'stddev_salary_change')
    ->eval('MV_SORT(VALUES(first_name))', 'sorted_names')
    ->orderBy('avg_max_salary_change', 'DESC');

echo $query5->toEsql() . "\n\n";

// ======= Exemple 6: Analyse de produits avec WEIGHTED_AVG =======
echo "6. Analyse de produits avec moyenne pondérée:\n";
$query6 = (new QueryBuilder())
    ->from('products')
    ->groupBy(['category'])
    ->eval(QueryBuilder::count('*'), 'product_count')
    ->eval(QueryBuilder::weightedAvg('price', 'sales_volume'), 'weighted_avg_price')
    ->eval(QueryBuilder::weightedAvg('rating', 'review_count'), 'weighted_avg_rating')
    ->eval(QueryBuilder::top('price', 5, 'desc'), 'top_5_prices')
    ->eval(QueryBuilder::values('brand'), 'available_brands')
    ->orderBy('weighted_avg_price', 'DESC');

echo $query6->toEsql() . "\n\n";

// ======= Exemple 7: Analyse statistique complète =======
echo "7. Analyse statistique complète des salaires:\n";
$query7 = (new QueryBuilder())
    ->from('employees')
    ->where('active', '=', true)
    ->groupBy(['department', 'level'])
    ->eval(QueryBuilder::count('*'), 'employee_count')
    ->eval(QueryBuilder::min('salary'), 'min_salary')
    ->eval(QueryBuilder::max('salary'), 'max_salary')
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->eval(QueryBuilder::median('salary'), 'median_salary')
    ->eval(QueryBuilder::percentile('salary', 25), 'q1_salary')
    ->eval(QueryBuilder::percentile('salary', 75), 'q3_salary')
    ->eval(QueryBuilder::stdDev('salary'), 'salary_stddev')
    ->eval(QueryBuilder::medianAbsoluteDeviation('salary'), 'salary_mad')
    ->orderBy('department')
    ->orderBy('level');

echo $query7->toEsql() . "\n\n";

echo "=== Fin des exemples ===\n";
