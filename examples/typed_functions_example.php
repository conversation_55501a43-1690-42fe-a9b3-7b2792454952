<?php

require_once __DIR__ . '/../vendor/autoload.php';

use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedTop;
use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedPercentile;
use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedCountDistinct;

echo "=== Exemples des fonctions typées ESQL ===\n\n";

// ======= Exemple 1: Fonction TOP avec validation automatique =======
echo "1. Fonction TOP avec validation automatique:\n";

try {
    // Utilisation correcte
    $top1 = new TypedTop('salary', 5, 'desc');
    echo "✓ TOP valide: " . $top1->toString() . "\n";

    // Ordre case-insensitive automatiquement normalisé
    $top2 = new TypedTop('salary', 3, 'ASC');
    echo "✓ TOP avec normalisation: " . $top2->toString() . "\n";

    // Valeur par défaut pour l'ordre
    $top3 = new TypedTop('salary', 10);
    echo "✓ TOP avec valeur par défaut: " . $top3->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 2: Validation des erreurs =======
echo "2. Validation automatique des erreurs:\n";

try {
    // Ordre invalide
    $topInvalid = new TypedTop('salary', 5, 'invalid');
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur détectée pour ordre invalide: " . $e->getMessage() . "\n";
}

try {
    // Limite négative
    $topInvalid = new TypedTop('salary', -1);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur détectée pour limite négative: " . $e->getMessage() . "\n";
}

try {
    // Limite zéro
    $topInvalid = new TypedTop('salary', 0);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur détectée pour limite zéro: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 3: Fonction PERCENTILE avec validation de plage =======
echo "3. Fonction PERCENTILE avec validation de plage:\n";

try {
    // Percentiles valides
    $p1 = new TypedPercentile('salary', 95);
    echo "✓ PERCENTILE valide: " . $p1->toString() . "\n";

    $p2 = new TypedPercentile('response_time', 99.9);
    echo "✓ PERCENTILE avec décimale: " . $p2->toString() . "\n";

    // Valeurs limites
    $p3 = new TypedPercentile('data', 0);
    echo "✓ PERCENTILE minimum: " . $p3->toString() . "\n";

    $p4 = new TypedPercentile('data', 100);
    echo "✓ PERCENTILE maximum: " . $p4->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 4: Validation des erreurs PERCENTILE =======
echo "4. Validation des erreurs PERCENTILE:\n";

try {
    // Percentile > 100
    $pInvalid = new TypedPercentile('salary', 101);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur détectée pour percentile > 100: " . $e->getMessage() . "\n";
}

try {
    // Percentile négatif
    $pInvalid = new TypedPercentile('salary', -5);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur détectée pour percentile négatif: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 5: Fonction COUNT_DISTINCT avec paramètre optionnel =======
echo "5. Fonction COUNT_DISTINCT avec paramètre optionnel:\n";

try {
    // Sans précision
    $cd1 = new TypedCountDistinct('user_id');
    echo "✓ COUNT_DISTINCT sans précision: " . $cd1->toString() . "\n";

    // Avec précision
    $cd2 = new TypedCountDistinct('user_id', 1000);
    echo "✓ COUNT_DISTINCT avec précision: " . $cd2->toString() . "\n";

    // Avec expression de champ complexe
    $cd3 = new TypedCountDistinct('MV_DEDUPE(tags)', 500);
    echo "✓ COUNT_DISTINCT avec expression: " . $cd3->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 6: Validation des erreurs COUNT_DISTINCT =======
echo "6. Validation des erreurs COUNT_DISTINCT:\n";

try {
    // Précision zéro
    $cdInvalid = new TypedCountDistinct('user_id', 0);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur détectée pour précision zéro: " . $e->getMessage() . "\n";
}

try {
    // Précision négative
    $cdInvalid = new TypedCountDistinct('user_id', -100);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur détectée pour précision négative: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 7: Expressions de champs complexes =======
echo "7. Support des expressions de champs complexes:\n";

try {
    // Expression mathématique
    $top1 = new TypedTop('salary * 1.2', 5, 'desc');
    echo "✓ TOP avec expression mathématique: " . $top1->toString() . "\n";

    // Appel de fonction imbriqué
    $p1 = new TypedPercentile('ROUND(MV_AVG(scores), 2)', 90);
    echo "✓ PERCENTILE avec fonction imbriquée: " . $p1->toString() . "\n";

    // Référence de champ avec point
    $cd1 = new TypedCountDistinct('employee.department');
    echo "✓ COUNT_DISTINCT avec champ imbriqué: " . $cd1->toString() . "\n";

    // Astérisque
    $cd2 = new TypedCountDistinct('*');
    echo "✓ COUNT_DISTINCT avec astérisque: " . $cd2->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 8: Introspection des types d'arguments =======
echo "8. Introspection des types d'arguments:\n";

$top = new TypedTop('salary', 3, 'desc');
$argumentTypes = $top->getArgumentTypes();

echo "Fonction TOP a " . count($argumentTypes) . " arguments:\n";
foreach ($argumentTypes as $index => $type) {
    $className = get_class($type);
    $shortName = substr($className, strrpos($className, '\\') + 1);
    echo "  - Argument $index: $shortName\n";
    
    if ($type instanceof \EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument) {
        $wrappedType = $type->getWrappedType();
        $wrappedClassName = get_class($wrappedType);
        $wrappedShortName = substr($wrappedClassName, strrpos($wrappedClassName, '\\') + 1);
        echo "    (Optionnel: $wrappedShortName)\n";
        
        if ($wrappedType instanceof \EsqlPhp\QueryBuilder\Functions\ArgumentTypes\EnumArgument) {
            echo "    Valeurs autorisées: " . implode(', ', $wrappedType->getAllowedValues()) . "\n";
        }
    }
}

echo "\n=== Fin des exemples ===\n";
