<?php

require_once __DIR__ . '/../vendor/autoload.php';

use EsqlPhp\QueryBuilder\QueryBuilder;

echo "=== Comparaison fonctions typées vs non-typées ===\n\n";

// ======= Exemple 1: Fonctions non-typées (existantes) =======
echo "1. Fonctions non-typées (comportement actuel):\n";

try {
    // Ces fonctions acceptent tout et ne valident pas
    $top1 = QueryBuilder::top('salary', 5, 'desc');
    echo "✓ TOP non-typé: " . $top1->toString() . "\n";

    $percentile1 = QueryBuilder::percentile('salary', 95);
    echo "✓ PERCENTILE non-typé: " . $percentile1->toString() . "\n";

    $countDistinct1 = QueryBuilder::countDistinct('user_id', 1000);
    echo "✓ COUNT_DISTINCT non-typé: " . $countDistinct1->toString() . "\n";

    // Ces fonctions acceptent même des valeurs invalides sans erreur
    $topInvalid = QueryBuilder::top('salary', 5, 'invalid_order');
    echo "⚠ TOP avec ordre invalide accepté: " . $topInvalid->toString() . "\n";

    $percentileInvalid = QueryBuilder::percentile('salary', 150);
    echo "⚠ PERCENTILE > 100 accepté: " . $percentileInvalid->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 2: Fonctions typées (nouvelles) =======
echo "2. Fonctions typées (avec validation automatique):\n";

try {
    // Ces fonctions valident automatiquement les arguments
    $typedTop1 = QueryBuilder::typedTop('salary', 5, 'desc');
    echo "✓ TOP typé valide: " . $typedTop1->toString() . "\n";

    $typedPercentile1 = QueryBuilder::typedPercentile('salary', 95);
    echo "✓ PERCENTILE typé valide: " . $typedPercentile1->toString() . "\n";

    $typedCountDistinct1 = QueryBuilder::typedCountDistinct('user_id', 1000);
    echo "✓ COUNT_DISTINCT typé valide: " . $typedCountDistinct1->toString() . "\n";

    // Normalisation automatique de la casse
    $typedTop2 = QueryBuilder::typedTop('salary', 3, 'ASC');
    echo "✓ TOP typé avec normalisation: " . $typedTop2->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 3: Validation des erreurs avec fonctions typées =======
echo "3. Validation automatique des erreurs (fonctions typées):\n";

try {
    $typedTopInvalid = QueryBuilder::typedTop('salary', 5, 'invalid_order');
    echo "✗ Cette ligne ne devrait pas s'afficher\n";
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur ordre invalide détectée: " . $e->getMessage() . "\n";
}

try {
    $typedPercentileInvalid = QueryBuilder::typedPercentile('salary', 150);
    echo "✗ Cette ligne ne devrait pas s'afficher\n";
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur percentile > 100 détectée: " . $e->getMessage() . "\n";
}

try {
    $typedTopNegative = QueryBuilder::typedTop('salary', -1);
    echo "✗ Cette ligne ne devrait pas s'afficher\n";
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur limite négative détectée: " . $e->getMessage() . "\n";
}

try {
    $typedCountDistinctInvalid = QueryBuilder::typedCountDistinct('user_id', 0);
    echo "✗ Cette ligne ne devrait pas s'afficher\n";
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur précision zéro détectée: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 4: Utilisation dans une requête complète =======
echo "4. Utilisation dans une requête complète:\n";

try {
    $query = (new QueryBuilder())
        ->from('employees')
        ->groupBy(['department'])
        ->eval(QueryBuilder::typedCountDistinct('job_title'), 'unique_roles')
        ->eval(QueryBuilder::typedPercentile('salary', 95), 'p95_salary')
        ->eval(QueryBuilder::typedTop('salary', 3, 'desc'), 'top_salaries')
        ->orderBy('p95_salary', 'DESC');

    echo "✓ Requête avec fonctions typées:\n";
    echo $query->toEsql() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur dans la requête: " . $e->getMessage() . "\n";
}

echo "\n";

// ======= Exemple 5: Avantages du système typé =======
echo "5. Résumé des avantages du système typé:\n";
echo "✓ Validation automatique des arguments\n";
echo "✓ Messages d'erreur clairs et précis\n";
echo "✓ Normalisation automatique des valeurs (ex: 'ASC' -> 'asc')\n";
echo "✓ Quotage intelligent des chaînes\n";
echo "✓ Documentation intégrée des contraintes\n";
echo "✓ Détection précoce des erreurs (au moment de la construction)\n";
echo "✓ Introspection des types d'arguments\n";
echo "✓ Compatibilité avec l'API existante\n";

echo "\n";

// ======= Exemple 6: Introspection des types =======
echo "6. Introspection des types d'arguments:\n";

$typedTop = QueryBuilder::typedTop('salary', 5, 'desc');
$argumentTypes = $typedTop->getArgumentTypes();

echo "La fonction TOP typée a " . count($argumentTypes) . " arguments:\n";
foreach ($argumentTypes as $index => $type) {
    $className = get_class($type);
    $shortName = substr($className, strrpos($className, '\\') + 1);
    echo "  - Argument $index: $shortName";
    
    if ($type instanceof \EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument) {
        $wrappedType = $type->getWrappedType();
        $wrappedClassName = get_class($wrappedType);
        $wrappedShortName = substr($wrappedClassName, strrpos($wrappedClassName, '\\') + 1);
        echo " (Optionnel: $wrappedShortName)";
        
        if ($wrappedType instanceof \EsqlPhp\QueryBuilder\Functions\ArgumentTypes\EnumArgument) {
            echo " [" . implode(', ', $wrappedType->getAllowedValues()) . "]";
        }
    }
    echo "\n";
}

echo "\n=== Fin de la comparaison ===\n";
