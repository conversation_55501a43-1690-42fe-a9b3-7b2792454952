<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Clauses;

use EsqlPhp\QueryBuilder\ClauseInterface;
use EsqlPhp\QueryBuilder\ExpressionInterface;

class EvalClause implements ClauseInterface
{
    private array $expressions = [];
    
    /**
     * @param string|ExpressionInterface $expression L'expression à évaluer
     * @param string|null $alias L'alias pour le résultat
     * @return void
     */
    public function addExpression($expression, ?string $alias = null): void
    {
        $this->expressions[] = [
            'expression' => $expression,
            'alias' => $alias
        ];
    }
    
    public function toString(): string
    {
        if (empty($this->expressions)) {
            return '';
        }

        $parts = [];
        foreach ($this->expressions as $expr) {
            $expression = $expr['expression'];
            $expressionStr = $expression instanceof ExpressionInterface
                ? $expression->toString()
                : $expression;

            if ($expr['alias'] !== null) {
                // Syntaxe ESQL: alias = expression
                $part = $expr['alias'] . ' = ' . $expressionStr;
            } else {
                $part = $expressionStr;
            }

            // Chaque expression EVAL doit être sur sa propre ligne
            $parts[] = '| EVAL ' . $part;
        }

        return implode("\n", $parts);
    }
}
