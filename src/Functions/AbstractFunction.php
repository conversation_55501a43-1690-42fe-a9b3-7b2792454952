<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions;

abstract class AbstractFunction implements FunctionInterface
{
    protected string $name;
    protected array $arguments = [];
    
    public function __construct(string $name, array $arguments = [])
    {
        $this->name = $name;
        $this->arguments = $arguments;
    }
    
    /**
     * Formate un argument selon son type et le contexte de la fonction.
     *
     * @param mixed $arg L'argument à formater
     * @return string L'argument formaté
     */
    protected function formatArgument($arg): string
    {
        // Si c'est une instance de FunctionInterface, utiliser sa méthode toString
        if ($arg instanceof FunctionInterface) {
            return $arg->toString();
        }
        
        // Si c'est une valeur null
        if (is_null($arg)) {
            return 'NULL';
        }
        
        // Si c'est une valeur booléenne
        if (is_bool($arg)) {
            return $arg ? 'true' : 'false';
        }
        
        // Si c'est un tableau
        if (is_array($arg)) {
            $items = array_map([$this, 'formatArgument'], $arg);
            return '[' . implode(', ', $items) . ']';
        }
        
        // Si c'est un nombre
        if (is_numeric($arg) && !is_string($arg)) {
            return (string) $arg;
        }
        
        // À partir d'ici, on traite les chaînes de caractères
        if (is_string($arg)) {
            
            // Si c'est une référence de champ, on la laisse telle quelle
            if ($this->isFieldReference($arg)) {
                return $arg;
            }
            
            // Si c'est un appel de fonction, on le laisse tel quel
            if ($this->isFunctionCall($arg)) {
                return $arg;
            }
            
            // Si c'est un astérisque seul, on ne met pas de guillemets
            if ($arg === '*') {
                return $arg;
            }
            
            // Si c'est déjà une chaîne entre guillemets, on la laisse telle quelle
            if (preg_match("/^'.*'$/", $arg)) {
                return $arg;
            }
            
            // Si c'est une expression mathématique, ne pas ajouter de guillemets
            if ($this->isMathExpression($arg)) {
                return $arg;
            }
            
            // Pour les fonctions de chaînes, les unités de temps et les formats de date, 
            // on doit mettre des guillemets
            if ($this->requiresQuotes($arg)) {
                return "'" . str_replace("'", "''", $arg) . "'";
            }
            
            // Par défaut, on considère que c'est une chaîne littérale et on ajoute des guillemets
            return "'" . str_replace("'", "''", $arg) . "'";
        }
        
        // Pour tout autre type, on convertit en chaîne
        return (string) $arg;
    }
    
    /**
     * Détermine si une chaîne est une expression mathématique.
     *
     * @param string $value La chaîne à vérifier
     * @return bool True si c'est une expression mathématique, false sinon
     */
    protected function isMathExpression(string $value): bool
    {
        // Vérifie si la chaîne contient des opérateurs mathématiques
        return preg_match('/^[\+\-\*\/\(\)0-9\s\.]+$/', $value) === 1 ||
               strpos($value, ' * ') !== false ||
               strpos($value, ' + ') !== false ||
               strpos($value, ' - ') !== false ||
               strpos($value, ' / ') !== false;
    }
    
    /**
     * Détermine si un argument nécessite des guillemets en fonction du contexte.
     *
     * @param string $value La valeur à vérifier
     * @return bool True si des guillemets sont nécessaires, false sinon
     */
    protected function requiresQuotes(string $value): bool
    {
        // Les fonctions de chaînes nécessitent toujours des guillemets pour les arguments littéraux
        $stringFunctions = ['concat', 'length', 'substring', 'lowercase', 'uppercase'];
        if (in_array($this->name, $stringFunctions)) {
            return true;
        }
        
        // Les unités de temps pour date_trunc nécessitent des guillemets
        if ($this->name === 'date_trunc' && in_array($value, ['year', 'month', 'day', 'hour', 'minute', 'second'])) {
            return true;
        }
        
        // Les formats de date pour date_format nécessitent des guillemets
        if ($this->name === 'date_format' && preg_match('/^[yMdHms\-\s:\/]+$/', $value)) {
            return true;
        }
        
        // Par défaut, on considère que les guillemets sont nécessaires pour les chaînes littérales
        return true;
    }
    
    protected function isFieldReference(string $value): bool
    {
        return preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$/', $value) === 1;
    }
    
    protected function isFunctionCall(string $value): bool
    {
        return preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*\(.*\)$/', $value) === 1;
    }
    
    public function toString(): string
    {
        $args = array_map([$this, 'formatArgument'], $this->arguments);
        return $this->name . '(' . implode(', ', $args) . ')';
    }
}
