<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\AbstractFunction;

/**
 * @see https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions#esql-count_distinct
 */
class CountDistinct extends AbstractFunction
{
    public function __construct($field, ?int $precision = null)
    {
        $arguments = [$field];
        if ($precision !== null) {
            $arguments[] = $precision;
        }
        parent::__construct('count_distinct', $arguments);
    }
}
