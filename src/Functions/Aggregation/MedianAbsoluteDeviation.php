<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\AbstractFunction;

/**
 * @see https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions#esql-median_absolute_deviation
 */
class MedianAbsoluteDeviation extends AbstractFunction
{
    public function __construct($field)
    {
        parent::__construct('median_absolute_deviation', [$field]);
    }
}
