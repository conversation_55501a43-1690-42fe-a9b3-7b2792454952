<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\AbstractFunction;

/**
 * @see https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions#esql-percentile
 */
class Percentile extends AbstractFunction
{
    public function __construct($field, $percentile)
    {
        parent::__construct('percentile', [$field, $percentile]);
    }
}
