<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\TypedAbstractFunction;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\FieldArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\NumericArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument;

/**
 * Typed version of COUNT_DISTINCT aggregation function
 * @see https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions#esql-count_distinct
 */
class TypedCountDistinct extends TypedAbstractFunction
{
    public function __construct($field, ?int $precision = null)
    {
        $args = [$field];
        if ($precision !== null) {
            $args[] = $precision;
        }
        parent::__construct('count_distinct', $args);
    }

    protected function defineArgumentTypes(): void
    {
        // Field argument - can be a field reference or expression
        $this->addArgumentType(new FieldArgument(''));

        // Precision argument - optional positive integer
        $precisionArg = new NumericArgument(0, [
            'integer' => true,
            'positive' => true,
            'min' => 1
        ]);
        $this->addArgumentType(new OptionalArgument($precisionArg));
    }
}
