<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\TypedAbstractFunction;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\FieldArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\NumericArgument;

/**
 * Typed version of PERCENTILE aggregation function
 * @see https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions#esql-percentile
 */
class TypedPercentile extends TypedAbstractFunction
{
    public function __construct($field, $percentile)
    {
        parent::__construct('percentile', [$field, $percentile]);
    }

    protected function defineArgumentTypes(): void
    {
        // Field argument - can be a field reference or expression
        $this->addArgumentType(new FieldArgument(''));

        // Percentile argument - must be a number between 0 and 100
        $this->addArgumentType(new NumericArgument(0, [
            'min' => 0,
            'max' => 100,
            'non_negative' => true
        ]));
    }
}
