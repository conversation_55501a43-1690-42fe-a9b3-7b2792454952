<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\TypedAbstractFunction;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\FieldArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\NumericArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\EnumArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument;

/**
 * Typed version of TOP aggregation function
 * @see https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions#esql-top
 */
class TypedTop extends TypedAbstractFunction
{
    public function __construct($field, int $limit, string $order = 'asc')
    {
        parent::__construct('top', [$field, $limit, $order]);
    }

    protected function defineArgumentTypes(): void
    {
        // Field argument - can be a field reference or expression
        $this->addArgumentType(new FieldArgument(''));

        // Limit argument - must be a positive integer
        $this->addArgumentType(new NumericArgument(1, [
            'integer' => true,
            'positive' => true,
            'min' => 1
        ]));

        // Order argument - must be 'asc' or 'desc', defaults to 'asc'
        $this->addArgumentType(new OptionalArgument(new EnumArgument('asc', ['asc', 'desc'], false), 'asc'));
    }
}
