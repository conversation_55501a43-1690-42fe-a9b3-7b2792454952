<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\AbstractFunction;

/**
 * @see https://www.elastic.co/docs/reference/query-languages/esql/functions-operators/aggregation-functions#esql-weighted_avg
 */
class WeightedAvg extends AbstractFunction
{
    public function __construct($field, $weight)
    {
        parent::__construct('weighted_avg', [$field, $weight]);
    }
}
