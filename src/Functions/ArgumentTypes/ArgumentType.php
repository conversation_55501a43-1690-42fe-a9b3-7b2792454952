<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\ArgumentTypes;

/**
 * Base class for defining argument types with validation and formatting rules
 */
abstract class ArgumentType
{
    protected $value;
    protected array $constraints = [];

    public function __construct($value, array $constraints = [])
    {
        $this->value = $value;
        $this->constraints = $constraints;
        $this->validate();
    }

    /**
     * Validate the argument value against constraints
     */
    abstract protected function validate(): void;

    /**
     * Format the argument for ESQL output
     */
    abstract public function format(): string;

    /**
     * Get the raw value
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Get constraints
     */
    public function getConstraints(): array
    {
        return $this->constraints;
    }

    /**
     * Check if a string is a field reference
     */
    protected function isFieldReference(string $value): bool
    {
        return preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$/', $value) === 1;
    }

    /**
     * Check if a string is a function call
     */
    protected function isFunctionCall(string $value): bool
    {
        return preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*\(.*\)$/', $value) === 1;
    }

    /**
     * Check if a string is a math expression
     */
    protected function isMathExpression(string $value): bool
    {
        return preg_match('/^[\+\-\*\/\(\)0-9\s\.]+$/', $value) === 1 ||
               strpos($value, ' * ') !== false ||
               strpos($value, ' + ') !== false ||
               strpos($value, ' - ') !== false ||
               strpos($value, ' / ') !== false;
    }

    /**
     * Escape single quotes in string values
     */
    protected function escapeString(string $value): string
    {
        return str_replace("'", "''", $value);
    }

    /**
     * Create a new instance of this argument type with a different value
     * This method should be overridden by subclasses that have complex constructors
     */
    public function withValue($newValue): self
    {
        return new static($newValue, $this->constraints);
    }
}
