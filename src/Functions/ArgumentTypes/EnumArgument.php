<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\ArgumentTypes;

use InvalidArgumentException;

/**
 * Represents an enumerated argument with a fixed set of allowed values
 */
class EnumArgument extends ArgumentType
{
    private array $allowedValues;
    private bool $caseSensitive;

    public function __construct($value, array $allowedValues, bool $caseSensitive = true)
    {
        $this->allowedValues = $allowedValues;
        $this->caseSensitive = $caseSensitive;
        parent::__construct($value, ['allowed_values' => $allowedValues, 'case_sensitive' => $caseSensitive]);
    }

    protected function validate(): void
    {
        if (!is_string($this->value)) {
            throw new InvalidArgumentException('Enum argument must be a string');
        }

        $valueToCheck = $this->caseSensitive ? $this->value : strtolower($this->value);
        $allowedToCheck = $this->caseSensitive ? $this->allowedValues : array_map('strtolower', $this->allowedValues);

        var_dump($this->allowedValues);
        
        if (!in_array($valueToCheck, $allowedToCheck, true)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Invalid value "%s". Allowed values are: %s',
                    $this->value,
                    implode(', ', $this->allowedValues)
                )
            );
        }
    }

    public function format(): string
    {
        // Normalize case if not case sensitive
        if (!$this->caseSensitive) {
            $normalizedValue = strtolower($this->value);
            $index = array_search($normalizedValue, array_map('strtolower', $this->allowedValues), true);
            if ($index !== false) {
                $this->value = $this->allowedValues[$index];
            }
        }

        // Quote the enum value
        return "'" . $this->escapeString($this->value) . "'";
    }

    public function getAllowedValues(): array
    {
        return $this->allowedValues;
    }

    public function isCaseSensitive(): bool
    {
        return $this->caseSensitive;
    }
}
