<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\ArgumentTypes;

/**
 * Wrapper for optional arguments
 */
class OptionalArgument extends ArgumentType
{
    private ArgumentType $wrappedType;
    private $defaultValue;

    public function __construct(ArgumentType $wrappedType, $defaultValue = null)
    {
        $this->wrappedType = $wrappedType;
        $this->defaultValue = $defaultValue;
        parent::__construct($wrappedType->getValue(), $wrappedType->getConstraints());
    }

    protected function validate(): void
    {
        // Validation is handled by the wrapped type
        // Optional arguments are validated only if they have a value
        if ($this->value !== null) {
            $this->wrappedType->validate();
        }
    }

    public function format(): string
    {
        if ($this->value === null) {
            if ($this->defaultValue !== null) {
                return (string) $this->defaultValue;
            }
            return '';
        }

        return $this->wrappedType->format();
    }

    public function getWrappedType(): ArgumentType
    {
        return $this->wrappedType;
    }

    public function getDefaultValue()
    {
        return $this->defaultValue;
    }

    public function hasValue(): bool
    {
        return $this->value !== null;
    }
}
