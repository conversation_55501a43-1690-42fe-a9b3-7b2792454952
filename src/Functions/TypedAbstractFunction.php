<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions;

use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\ArgumentType;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument;

/**
 * Enhanced abstract function with typed arguments and validation
 */
abstract class TypedAbstractFunction implements FunctionInterface
{
    protected string $name;
    protected array $argumentTypes = [];
    protected array $argumentValues = [];

    public function __construct(string $name, array $arguments = [])
    {
        $this->name = $name;
        $this->defineArgumentTypes();
        $this->setArguments($arguments);
    }

    /**
     * Define the argument types for this function
     * Should be implemented by each function to specify its argument schema
     */
    abstract protected function defineArgumentTypes(): void;

    /**
     * Add an argument type definition
     */
    protected function addArgumentType(ArgumentType $type): void
    {
        $this->argumentTypes[] = $type;
    }

    /**
     * Set the argument values and validate them
     */
    protected function setArguments(array $arguments): void
    {
        $this->argumentValues = [];

        foreach ($arguments as $index => $value) {
            if (!isset($this->argumentTypes[$index])) {
                throw new \InvalidArgumentException(
                    sprintf('Too many arguments provided for function %s', $this->name)
                );
            }

            $argumentType = $this->argumentTypes[$index];
            
            // Create a new instance of the argument type with the provided value
            $typedArgument = $this->createTypedArgument($argumentType, $value);
            $this->argumentValues[] = $typedArgument;
        }

        // Check for missing required arguments
        for ($i = count($arguments); $i < count($this->argumentTypes); $i++) {
            $argumentType = $this->argumentTypes[$i];
            if (!($argumentType instanceof OptionalArgument)) {
                throw new \InvalidArgumentException(
                    sprintf('Missing required argument at position %d for function %s', $i, $this->name)
                );
            }
            // Add the optional argument with null value
            $this->argumentValues[] = $argumentType;
        }
    }

    /**
     * Create a typed argument instance from a type template and value
     */
    private function createTypedArgument(ArgumentType $template, $value): ArgumentType
    {
        $className = get_class($template);
        
        if ($template instanceof OptionalArgument) {
            $wrappedType = $template->getWrappedType();
            $wrappedClassName = get_class($wrappedType);
            
            if ($value === null) {
                return $template; // Return the template with null value
            }
            
            // Create new instance of wrapped type with the value
            $newWrappedType = new $wrappedClassName($value, $wrappedType->getConstraints());
            return new OptionalArgument($newWrappedType, $template->getDefaultValue());
        }

        // For non-optional arguments, create new instance with the value
        return new $className($value, $template->getConstraints());
    }

    /**
     * Get the formatted arguments for ESQL output
     */
    protected function getFormattedArguments(): array
    {
        $formatted = [];
        
        foreach ($this->argumentValues as $argument) {
            if ($argument instanceof OptionalArgument && !$argument->hasValue()) {
                continue; // Skip optional arguments without values
            }
            
            $formatted[] = $argument->format();
        }
        
        return $formatted;
    }

    public function toString(): string
    {
        $args = $this->getFormattedArguments();
        return $this->name . '(' . implode(', ', $args) . ')';
    }

    /**
     * Get argument types for documentation/introspection
     */
    public function getArgumentTypes(): array
    {
        return $this->argumentTypes;
    }

    /**
     * Get argument values
     */
    public function getArgumentValues(): array
    {
        return $this->argumentValues;
    }
}
