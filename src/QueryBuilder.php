<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder;

use EsqlPhp\QueryBuilder\Clauses\EvalClause;
use EsqlPhp\QueryBuilder\Clauses\FieldsClause;
use EsqlPhp\QueryBuilder\ExpressionInterface;
use EsqlPhp\QueryBuilder\Clauses\FromClause;
use EsqlPhp\QueryBuilder\Clauses\WhereClause;
use EsqlPhp\QueryBuilder\Clauses\SortClause;
use EsqlPhp\QueryBuilder\Clauses\LimitClause;
use EsqlPhp\QueryBuilder\Clauses\GroupByClause;
use EsqlPhp\QueryBuilder\Clauses\KeepClause;
use EsqlPhp\QueryBuilder\Commands\DropCommand;
use EsqlPhp\QueryBuilder\Commands\RenameCommand;
use EsqlPhp\QueryBuilder\Commands\EnrichCommand;
use EsqlPhp\QueryBuilder\Commands\MeltCommand;
use EsqlPhp\QueryBuilder\Commands\DissolveCommand;
use EsqlPhp\QueryBuilder\Functions\Math\Abs;
use EsqlPhp\QueryBuilder\Functions\Math\Ceil;
use EsqlPhp\QueryBuilder\Functions\Math\Floor;
use EsqlPhp\QueryBuilder\Functions\Math\Round;
use EsqlPhp\QueryBuilder\Functions\String\Concat;
use EsqlPhp\QueryBuilder\Functions\String\Length;
use EsqlPhp\QueryBuilder\Functions\String\Substring;
use EsqlPhp\QueryBuilder\Functions\String\LowerCase;
use EsqlPhp\QueryBuilder\Functions\String\UpperCase;
use EsqlPhp\QueryBuilder\Functions\Date\Now;
use EsqlPhp\QueryBuilder\Functions\Date\DateFormat;
use EsqlPhp\QueryBuilder\Functions\Date\DateTrunc;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Count;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Sum;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Avg;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Min;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Max;
use EsqlPhp\QueryBuilder\Functions\Aggregation\CountDistinct;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Median;
use EsqlPhp\QueryBuilder\Functions\Aggregation\MedianAbsoluteDeviation;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Percentile;
use EsqlPhp\QueryBuilder\Functions\Aggregation\StCentroidAgg;
use EsqlPhp\QueryBuilder\Functions\Aggregation\StExtentAgg;
use EsqlPhp\QueryBuilder\Functions\Aggregation\StdDev;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Top;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Values;
use EsqlPhp\QueryBuilder\Functions\Aggregation\WeightedAvg;
use RuntimeException;

class QueryBuilder
{
    private ?FromClause $fromClause = null;
    private ?FieldsClause $fieldsClause = null;
    private ?WhereClause $whereClause = null;
    private ?SortClause $sortClause = null;
    private ?LimitClause $limitClause = null;
    private ?GroupByClause $groupByClause = null;
    private ?EvalClause $evalClause = null;
    private ?KeepClause $keepClause = null;
    private array $commands = [];

    /**
     * Spécifie l'index source de la requête.
     *
     * @param string $index Le nom de l'index
     * @return self
     */
    public function from(string $index): self
    {
        $this->fromClause = new FromClause($index);
        return $this;
    }

    /**
     * Ajoute une condition WHERE avec un opérateur ET.
     *
     * @param string $field Le champ à filtrer
     * @param string $operator L'opérateur de comparaison
     * @param mixed $value La valeur de comparaison
     * @return self
     */
    public function where(string $field, string $operator, $value): self
    {
        if ($this->whereClause === null) {
            $this->whereClause = new WhereClause();
        }
        
        $this->whereClause->addCondition($field, $operator, $value);
        return $this;
    }

    /**
     * Ajoute une condition WHERE avec un opérateur OU.
     *
     * @param string $field Le champ à filtrer
     * @param string $operator L'opérateur de comparaison
     * @param mixed $value La valeur de comparaison
     * @return self
     */
    public function orWhere(string $field, string $operator, $value): self
    {
        if ($this->whereClause === null) {
            $this->whereClause = new WhereClause();
        }
        
        $this->whereClause->or($field, $operator, $value);
        return $this;
    }

    /**
     * Spécifie le nombre maximum de résultats à retourner.
     *
     * @param int $limit Le nombre maximum de résultats
     * @param int|null $offset L'offset pour la pagination
     * @return self
     */
    public function limit(int $limit, ?int $offset = null): self
    {
        $this->limitClause = new LimitClause($limit, $offset);
        return $this;
    }

    /**
     * Ajoute un critère de tri.
     *
     * @param string $field Le champ pour le tri
     * @param string $direction La direction du tri (ASC ou DESC)
     * @return self
     */
    public function orderBy(string $field, string $direction = 'ASC'): self
    {
        if ($this->sortClause === null) {
            $this->sortClause = new SortClause();
        }
        
        $this->sortClause->addSort($field, $direction);
        return $this;
    }

    /**
     * Ajoute une clause GROUP BY.
     *
     * @param array $fields Les champs pour le regroupement
     * @return self
     */
    public function groupBy(array $fields): self
    {
        $this->groupByClause = new GroupByClause($fields);
        return $this;
    }

    /**
     * Ajoute une expression EVAL.
     *
     * @param string|ExpressionInterface $expression L'expression à évaluer
     * @param string|null $alias L'alias pour le résultat
     * @return self
     */
    public function eval($expression, ?string $alias = null): self
    {
        if ($this->evalClause === null) {
            $this->evalClause = new EvalClause();
        }
        
        $this->evalClause->addExpression($expression, $alias);
        return $this;
    }

    /**
     * Ajoute une clause KEEP pour conserver uniquement certains champs.
     *
     * @param array $fields Les champs à conserver
     * @return self
     */
    public function keep(array $fields): self
    {
        $this->keepClause = new KeepClause($fields);
        return $this;
    }

    /**
     * Ajoute une commande DROP pour supprimer des champs.
     *
     * @param array $fields Les champs à supprimer
     * @return self
     */
    public function drop(array $fields): self
    {
        $this->commands[] = new DropCommand($fields);
        return $this;
    }

    /**
     * Ajoute une commande RENAME pour renommer des champs.
     *
     * @param array $renames Les champs à renommer (old => new)
     * @return self
     */
    public function rename(array $renames): self
    {
        $this->commands[] = new RenameCommand($renames);
        return $this;
    }

    /**
     * Ajoute une commande ENRICH pour enrichir les données.
     *
     * @param string $policyName Le nom de la politique d'enrichissement
     * @param array $fields Les champs sur lesquels appliquer l'enrichissement
     * @return self
     */
    public function enrich(string $policyName, array $fields = []): self
    {
        $this->commands[] = new EnrichCommand($policyName, $fields);
        return $this;
    }

    /**
     * Ajoute une commande MELT pour pivoter les données.
     *
     * @param array $fields Les champs à pivoter
     * @param string|null $asField Le nom du champ pour les noms des champs pivotés
     * @param string|null $valuesField Le nom du champ pour les valeurs des champs pivotés
     * @return self
     */
    public function melt(array $fields, ?string $asField = null, ?string $valuesField = null): self
    {
        $this->commands[] = new MeltCommand($fields, $asField, $valuesField);
        return $this;
    }

    /**
     * Ajoute une commande DISSOLVE pour déplier les tableaux.
     *
     * @param array $fields Les champs à déplier
     * @return self
     */
    public function dissolve(array $fields): self
    {
        $this->commands[] = new DissolveCommand($fields);
        return $this;
    }

    /**
     * Crée une fonction d'agrégation COUNT.
     *
     * @param string $field Le champ à compter
     * @return Count
     */
    public static function count($field = '*'): Count
    {
        return new Count($field);
    }

    /**
     * Crée une fonction d'agrégation SUM.
     *
     * @param string $field Le champ à additionner
     * @return Sum
     */
    public static function sum(string $field): Sum
    {
        return new Sum($field);
    }

    /**
     * Crée une fonction d'agrégation AVG.
     *
     * @param string $field Le champ pour calculer la moyenne
     * @return Avg
     */
    public static function avg(string $field): Avg
    {
        return new Avg($field);
    }

    /**
     * Crée une fonction d'agrégation MIN.
     *
     * @param string $field Le champ pour trouver le minimum
     * @return Min
     */
    public static function min(string $field): Min
    {
        return new Min($field);
    }

    /**
     * Crée une fonction d'agrégation MAX.
     *
     * @param string $field Le champ pour trouver le maximum
     * @return Max
     */
    public static function max(string $field): Max
    {
        return new Max($field);
    }

    /**
     * Crée une fonction d'agrégation COUNT_DISTINCT.
     *
     * @param string $field Le champ pour compter les valeurs distinctes
     * @param int|null $precision La précision pour l'approximation
     * @return CountDistinct
     */
    public static function countDistinct(string $field, ?int $precision = null): CountDistinct
    {
        return new CountDistinct($field, $precision);
    }

    /**
     * Crée une fonction d'agrégation MEDIAN.
     *
     * @param string $field Le champ pour calculer la médiane
     * @return Median
     */
    public static function median(string $field): Median
    {
        return new Median($field);
    }

    /**
     * Crée une fonction d'agrégation MEDIAN_ABSOLUTE_DEVIATION.
     *
     * @param string $field Le champ pour calculer la déviation absolue médiane
     * @return MedianAbsoluteDeviation
     */
    public static function medianAbsoluteDeviation(string $field): MedianAbsoluteDeviation
    {
        return new MedianAbsoluteDeviation($field);
    }

    /**
     * Crée une fonction d'agrégation PERCENTILE.
     *
     * @param string $field Le champ pour calculer le percentile
     * @param mixed $percentile Le percentile à calculer
     * @return Percentile
     */
    public static function percentile(string $field, $percentile): Percentile
    {
        return new Percentile($field, $percentile);
    }

    /**
     * Crée une fonction d'agrégation ST_CENTROID_AGG.
     *
     * @param string $field Le champ géométrique pour calculer le centroïde
     * @return StCentroidAgg
     */
    public static function stCentroidAgg(string $field): StCentroidAgg
    {
        return new StCentroidAgg($field);
    }

    /**
     * Crée une fonction d'agrégation ST_EXTENT_AGG.
     *
     * @param string $field Le champ géométrique pour calculer l'étendue spatiale
     * @return StExtentAgg
     */
    public static function stExtentAgg(string $field): StExtentAgg
    {
        return new StExtentAgg($field);
    }

    /**
     * Crée une fonction d'agrégation STD_DEV.
     *
     * @param string $field Le champ pour calculer l'écart-type
     * @return StdDev
     */
    public static function stdDev(string $field): StdDev
    {
        return new StdDev($field);
    }

    /**
     * Crée une fonction d'agrégation TOP.
     *
     * @param string $field Le champ pour collecter les valeurs supérieures
     * @param int $limit Le nombre maximum de valeurs à collecter
     * @param string $order L'ordre de tri (asc ou desc)
     * @return Top
     */
    public static function top(string $field, int $limit, string $order = 'asc'): Top
    {
        return new Top($field, $limit, $order);
    }

    /**
     * Crée une fonction d'agrégation VALUES.
     *
     * @param string $field Le champ pour collecter les valeurs uniques
     * @return Values
     */
    public static function values(string $field): Values
    {
        return new Values($field);
    }

    /**
     * Crée une fonction d'agrégation WEIGHTED_AVG.
     *
     * @param string $field Le champ pour calculer la moyenne pondérée
     * @param mixed $weight Le poids pour la moyenne pondérée
     * @return WeightedAvg
     */
    public static function weightedAvg(string $field, $weight): WeightedAvg
    {
        return new WeightedAvg($field, $weight);
    }

    /**
     * Crée une fonction ABS.
     *
     * @param mixed $value La valeur pour calculer la valeur absolue
     * @return Abs
     */
    public static function abs($value): Abs
    {
        return new Abs($value);
    }

    /**
     * Crée une fonction CEIL.
     *
     * @param mixed $value La valeur à arrondir au supérieur
     * @return Ceil
     */
    public static function ceil($value): Ceil
    {
        return new Ceil($value);
    }

    /**
     * Crée une fonction FLOOR.
     *
     * @param mixed $value La valeur à arrondir à l'inférieur
     * @return Floor
     */
    public static function floor($value): Floor
    {
        return new Floor($value);
    }

    /**
     * Crée une fonction ROUND.
     *
     * @param mixed $value La valeur à arrondir
     * @param int|null $decimals Le nombre de décimales
     * @return Round
     */
    public static function round($value, ?int $decimals = null): Round
    {
        return new Round($value, $decimals);
    }

    /**
     * Crée une fonction CONCAT.
     *
     * @param array $strings Les chaînes à concaténer
     * @return Concat
     */
    public static function concat(array $strings): Concat
    {
        return new Concat($strings);
    }

    /**
     * Crée une fonction LENGTH.
     *
     * @param mixed $string La chaîne pour calculer la longueur
     * @return Length
     */
    public static function length($string): Length
    {
        return new Length($string);
    }

    /**
     * Crée une fonction SUBSTRING.
     *
     * @param mixed $string La chaîne pour extraire une sous-chaîne
     * @param int $start La position de départ
     * @param int|null $length La longueur de la sous-chaîne
     * @return Substring
     */
    public static function substring($string, int $start, ?int $length = null): Substring
    {
        return new Substring($string, $start, $length);
    }

    /**
     * Crée une fonction LOWERCASE.
     *
     * @param mixed $string La chaîne à convertir en minuscules
     * @return LowerCase
     */
    public static function lowercase($string): LowerCase
    {
        return new LowerCase($string);
    }

    /**
     * Crée une fonction UPPERCASE.
     *
     * @param mixed $string La chaîne à convertir en majuscules
     * @return UpperCase
     */
    public static function uppercase($string): UpperCase
    {
        return new UpperCase($string);
    }

    /**
     * Crée une fonction NOW.
     *
     * @return Now
     */
    public static function now(): Now
    {
        return new Now();
    }

    /**
     * Crée une fonction DATE_FORMAT.
     *
     * @param mixed $dateTime La date à formater
     * @param string $format Le format de date
     * @return DateFormat
     */
    public static function dateFormat($dateTime, string $format): DateFormat
    {
        return new DateFormat($dateTime, $format);
    }

    /**
     * Crée une fonction DATE_TRUNC.
     *
     * @param string $unit L'unité de troncature (year, month, day, etc.)
     * @param mixed $dateTime La date à tronquer
     * @return DateTrunc
     */
    public static function dateTrunc(string $unit, $dateTime): DateTrunc
    {
        return new DateTrunc($unit, $dateTime);
    }

    /**
     * Génère la requête ES|QL complète.
     *
     * @return string
     * @throws RuntimeException
     */
    public function toEsql(): string
    {
        $parts = [];

        // FROM clause (obligatoire)
        if ($this->fromClause === null) {
            throw new RuntimeException('FROM clause is required');
        }
        $parts[] = $this->fromClause->toString();

        // FIELDS clause
        if ($this->fieldsClause !== null) {
            array_unshift($parts, $this->fieldsClause->toString());
        }

        // WHERE clause
        if ($this->whereClause !== null) {
            $parts[] = $this->whereClause->toString();
        }

        // GROUP BY clause
        if ($this->groupByClause !== null) {
            $parts[] = $this->groupByClause->toString();
        }

        // EVAL clause
        if ($this->evalClause !== null) {
            $parts[] = $this->evalClause->toString();
        }

        // KEEP clause
        if ($this->keepClause !== null) {
            $parts[] = $this->keepClause->toString();
        }

        // Autres commandes
        foreach ($this->commands as $command) {
            $parts[] = $command->toString();
        }

        // SORT clause
        if ($this->sortClause !== null) {
            $parts[] = $this->sortClause->toString();
        }

        // LIMIT clause
        if ($this->limitClause !== null) {
            $parts[] = $this->limitClause->toString();
        }

        return implode(PHP_EOL, $parts);
    }

    /**
     * Alias de toEsql().
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->toEsql();
    }
}
