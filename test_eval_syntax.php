<?php

require_once 'vendor/autoload.php';

use EsqlPhp\QueryBuilder\QueryBuilder;

echo "=== Test de la nouvelle syntaxe EVAL ===\n\n";

// Test 1: Une seule expression EVAL
echo "Test 1: Une seule expression EVAL\n";
$query1 = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::avg('salary'), 'avg_salary');

echo $query1->toEsql() . "\n\n";

// Test 2: Plusieurs expressions EVAL
echo "Test 2: Plusieurs expressions EVAL\n";
$query2 = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->eval(QueryBuilder::count('*'), 'total_count')
    ->eval(QueryBuilder::max('salary'), 'max_salary');

echo $query2->toEsql() . "\n\n";

// Test 3: Expression EVAL sans alias
echo "Test 3: Expression EVAL sans alias\n";
$query3 = (new QueryBuilder())
    ->from('employees')
    ->eval('salary * 1.1');

echo $query3->toEsql() . "\n\n";

// Test 4: Mélange avec et sans alias
echo "Test 4: Mélange avec et sans alias\n";
$query4 = (new QueryBuilder())
    ->from('employees')
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->eval('salary * 1.1')
    ->eval(QueryBuilder::count('*'), 'total_count');

echo $query4->toEsql() . "\n\n";

// Test 5: Requête complète avec GROUP BY
echo "Test 5: Requête complète avec GROUP BY\n";
$query5 = (new QueryBuilder())
    ->from('employees')
    ->groupBy(['department'])
    ->eval(QueryBuilder::avg('salary'), 'avg_salary')
    ->eval(QueryBuilder::countDistinct('job_title'), 'unique_roles')
    ->eval(QueryBuilder::stdDev('salary'), 'salary_stddev')
    ->orderBy('avg_salary', 'DESC');

echo $query5->toEsql() . "\n\n";

echo "=== Fin des tests ===\n";
