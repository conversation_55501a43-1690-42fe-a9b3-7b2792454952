<?php

require_once 'vendor/autoload.php';

use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedTop;
use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedPercentile;
use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedCountDistinct;

echo "=== Test simple des fonctions typées ===\n\n";

try {
    // Test TypedTop
    echo "1. Test TypedTop:\n";
    $top1 = new TypedTop('salary', 5, 'desc');
    echo "✓ TOP avec desc: " . $top1->toString() . "\n";

    $top2 = new TypedTop('salary', 3, 'ASC');
    echo "✓ TOP avec ASC (normalisé): " . $top2->toString() . "\n";

    $top3 = new TypedTop('salary', 10);
    echo "✓ TOP avec défaut: " . $top3->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur TypedTop: " . $e->getMessage() . "\n";
}

echo "\n";

try {
    // Test TypedPercentile
    echo "2. Test TypedPercentile:\n";
    $p1 = new TypedPercentile('salary', 95);
    echo "✓ PERCENTILE 95: " . $p1->toString() . "\n";

    $p2 = new TypedPercentile('response_time', 99.9);
    echo "✓ PERCENTILE 99.9: " . $p2->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur TypedPercentile: " . $e->getMessage() . "\n";
}

echo "\n";

try {
    // Test TypedCountDistinct
    echo "3. Test TypedCountDistinct:\n";
    $cd1 = new TypedCountDistinct('user_id');
    echo "✓ COUNT_DISTINCT sans précision: " . $cd1->toString() . "\n";

    $cd2 = new TypedCountDistinct('user_id', 1000);
    echo "✓ COUNT_DISTINCT avec précision: " . $cd2->toString() . "\n";

} catch (Exception $e) {
    echo "✗ Erreur TypedCountDistinct: " . $e->getMessage() . "\n";
}

echo "\n";

// Test des erreurs
echo "4. Test des validations d'erreurs:\n";

try {
    $topInvalid = new TypedTop('salary', -1);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur limite négative détectée: " . $e->getMessage() . "\n";
}

try {
    $topInvalid = new TypedTop('salary', 5, 'invalid');
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur ordre invalide détectée: " . $e->getMessage() . "\n";
}

try {
    $pInvalid = new TypedPercentile('salary', 101);
} catch (InvalidArgumentException $e) {
    echo "✓ Erreur percentile > 100 détectée: " . $e->getMessage() . "\n";
}

echo "\n=== Fin des tests ===\n";
