<?php

namespace EsqlPhp\Tests\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\Percentile;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class PercentileTest extends TestCase
{
    /**
     * Test basic PERCENTILE function
     * Example from documentation: FROM employees | STATS p0_salary = PERCENTILE(salary, 0)
     */
    public function testBasicPercentile(): void
    {
        $percentile = new Percentile('salary', 0);
        $this->assertEquals('percentile(salary, 0)', $percentile->toString());
    }

    /**
     * Test PERCENTILE with different percentile values
     * Example from documentation: p50_salary = PERCENTILE(salary, 50), p99_salary = PERCENTILE(salary, 99)
     */
    public function testPercentileWithDifferentValues(): void
    {
        // Test 50th percentile (median)
        $p50 = new Percentile('salary', 50);
        $this->assertEquals('percentile(salary, 50)', $p50->toString());

        // Test 99th percentile
        $p99 = new Percentile('salary', 99);
        $this->assertEquals('percentile(salary, 99)', $p99->toString());

        // Test 95th percentile
        $p95 = new Percentile('salary', 95);
        $this->assertEquals('percentile(salary, 95)', $p95->toString());
    }

    /**
     * Test PERCENTILE with decimal values
     */
    public function testPercentileWithDecimalValues(): void
    {
        $percentile = new Percentile('response_time', 99.9);
        $this->assertEquals('percentile(response_time, 99.9)', $percentile->toString());
    }

    /**
     * Test multiple PERCENTILE functions in complete query from documentation example
     * FROM employees | STATS p0_salary = PERCENTILE(salary, 0), p50_salary = PERCENTILE(salary, 50), p99_salary = PERCENTILE(salary, 99)
     */
    public function testMultiplePercentilesInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::percentile('salary', 0), 'p0_salary')
            ->eval(QueryBuilder::percentile('salary', 50), 'p50_salary')
            ->eval(QueryBuilder::percentile('salary', 99), 'p99_salary');

        $expected = "FROM employees\n" .
                   "| EVAL p0_salary = percentile(salary, 0)\n" .
                   "| EVAL p50_salary = percentile(salary, 50)\n" .
                   "| EVAL p99_salary = percentile(salary, 99)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test PERCENTILE with inline function
     */
    public function testPercentileWithInlineFunction(): void
    {
        $percentile = new Percentile('MV_MAX(salary_change)', 95);
        $this->assertEquals('percentile(MV_MAX(salary_change), 95)', $percentile->toString());
    }

    /**
     * Test PERCENTILE grouped by department
     */
    public function testPercentileGroupedByDepartment(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::percentile('salary', 95), 'p95_salary')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL p95_salary = percentile(salary, 95)\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
