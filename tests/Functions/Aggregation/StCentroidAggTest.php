<?php

namespace EsqlPhp\Tests\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\StCentroidAgg;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class StCentroidAggTest extends TestCase
{
    /**
     * Test basic ST_CENTROID_AGG function
     * Example from documentation: FROM airports | WHERE country == "India" | STATS centroid = ST_CENTROID_AGG(location)
     */
    public function testBasicStCentroidAgg(): void
    {
        $stCentroidAgg = new StCentroidAgg('location');
        $this->assertEquals('st_centroid_agg(location)', $stCentroidAgg->toString());
    }

    /**
     * Test ST_CENTROID_AGG with different geometry fields
     */
    public function testStCentroidAggWithDifferentFields(): void
    {
        // Test with coordinates field
        $stCentroidAgg1 = new StCentroidAgg('coordinates');
        $this->assertEquals('st_centroid_agg(coordinates)', $stCentroidAgg1->toString());

        // Test with geo_point field
        $stCentroidAgg2 = new StCentroidAgg('geo_point');
        $this->assertEquals('st_centroid_agg(geo_point)', $stCentroidAgg2->toString());
    }

    /**
     * Test ST_CENTROID_AGG in complete query from documentation example
     * FROM airports | WHERE country == "India" | STATS centroid = ST_CENTROID_AGG(location)
     */
    public function testStCentroidAggInCompleteQueryFromDocumentation(): void
    {
        $query = (new QueryBuilder())
            ->from('airports')
            ->where('country', '=', 'India')
            ->eval(QueryBuilder::stCentroidAgg('location'), 'centroid');

        $expected = "FROM airports\n" .
                   "| WHERE country = 'India'\n" .
                   "| EVAL centroid = st_centroid_agg(location)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test ST_CENTROID_AGG grouped by region
     */
    public function testStCentroidAggGroupedByRegion(): void
    {
        $query = (new QueryBuilder())
            ->from('cities')
            ->groupBy(['region'])
            ->eval(QueryBuilder::stCentroidAgg('coordinates'), 'region_centroid')
            ->orderBy('region');

        $expected = "FROM cities\n" .
                   "| GROUP BY region\n" .
                   "| EVAL region_centroid = st_centroid_agg(coordinates)\n" .
                   "| SORT region ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test ST_CENTROID_AGG with filtering
     */
    public function testStCentroidAggWithFiltering(): void
    {
        $query = (new QueryBuilder())
            ->from('stores')
            ->where('type', '=', 'retail')
            ->where('active', '=', true)
            ->eval(QueryBuilder::stCentroidAgg('location'), 'retail_centroid');

        $expected = "FROM stores\n" .
                   "| WHERE type = 'retail'\n" .
                   "| WHERE active = true\n" .
                   "| EVAL retail_centroid = st_centroid_agg(location)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test ST_CENTROID_AGG with multiple geometry types
     */
    public function testStCentroidAggWithMultipleGeometryTypes(): void
    {
        $query = (new QueryBuilder())
            ->from('geographic_data')
            ->groupBy(['data_type'])
            ->eval(QueryBuilder::stCentroidAgg('geometry'), 'centroid')
            ->orderBy('data_type');

        $expected = "FROM geographic_data\n" .
                   "| GROUP BY data_type\n" .
                   "| EVAL centroid = st_centroid_agg(geometry)\n" .
                   "| SORT data_type ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
