<?php

namespace EsqlPhp\Tests\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\StExtentAgg;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class StExtentAggTest extends TestCase
{
    /**
     * Test basic ST_EXTENT_AGG function
     * Example from documentation: FROM airports | WHERE country == "India" | STATS extent = ST_EXTENT_AGG(location)
     */
    public function testBasicStExtentAgg(): void
    {
        $stExtentAgg = new StExtentAgg('location');
        $this->assertEquals('st_extent_agg(location)', $stExtentAgg->toString());
    }

    /**
     * Test ST_EXTENT_AGG with different geometry fields
     */
    public function testStExtentAggWithDifferentFields(): void
    {
        // Test with coordinates field
        $stExtentAgg1 = new StExtentAgg('coordinates');
        $this->assertEquals('st_extent_agg(coordinates)', $stExtentAgg1->toString());

        // Test with geo_shape field
        $stExtentAgg2 = new StExtentAgg('geo_shape');
        $this->assertEquals('st_extent_agg(geo_shape)', $stExtentAgg2->toString());
    }

    /**
     * Test ST_EXTENT_AGG in complete query from documentation example
     * FROM airports | WHERE country == "India" | STATS extent = ST_EXTENT_AGG(location)
     */
    public function testStExtentAggInCompleteQueryFromDocumentation(): void
    {
        $query = (new QueryBuilder())
            ->from('airports')
            ->where('country', '=', 'India')
            ->eval(QueryBuilder::stExtentAgg('location'), 'extent');

        $expected = "FROM airports\n" .
                   "| WHERE country = 'India'\n" .
                   "| EVAL extent = st_extent_agg(location)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test ST_EXTENT_AGG grouped by region
     */
    public function testStExtentAggGroupedByRegion(): void
    {
        $query = (new QueryBuilder())
            ->from('cities')
            ->groupBy(['region'])
            ->eval(QueryBuilder::stExtentAgg('boundaries'), 'region_extent')
            ->orderBy('region');

        $expected = "FROM cities\n" .
                   "| GROUP BY region\n" .
                   "| EVAL region_extent = st_extent_agg(boundaries)\n" .
                   "| SORT region ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test ST_EXTENT_AGG with filtering
     */
    public function testStExtentAggWithFiltering(): void
    {
        $query = (new QueryBuilder())
            ->from('protected_areas')
            ->where('protection_level', '=', 'national_park')
            ->where('area_size', '>', 1000)
            ->eval(QueryBuilder::stExtentAgg('boundaries'), 'parks_extent');

        $expected = "FROM protected_areas\n" .
                   "| WHERE protection_level = 'national_park'\n" .
                   "| WHERE area_size > 1000\n" .
                   "| EVAL parks_extent = st_extent_agg(boundaries)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test ST_EXTENT_AGG with multiple geometry types
     */
    public function testStExtentAggWithMultipleGeometryTypes(): void
    {
        $query = (new QueryBuilder())
            ->from('geographic_features')
            ->groupBy(['feature_type'])
            ->eval(QueryBuilder::stExtentAgg('geometry'), 'feature_extent')
            ->orderBy('feature_type');

        $expected = "FROM geographic_features\n" .
                   "| GROUP BY feature_type\n" .
                   "| EVAL feature_extent = st_extent_agg(geometry)\n" .
                   "| SORT feature_type ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test ST_EXTENT_AGG combined with ST_CENTROID_AGG
     */
    public function testStExtentAggCombinedWithStCentroidAgg(): void
    {
        $query = (new QueryBuilder())
            ->from('regions')
            ->groupBy(['continent'])
            ->eval(QueryBuilder::stExtentAgg('boundaries'), 'continent_extent')
            ->eval(QueryBuilder::stCentroidAgg('boundaries'), 'continent_centroid')
            ->orderBy('continent');

        $expected = "FROM regions\n" .
                   "| GROUP BY continent\n" .
                   "| EVAL continent_extent = st_extent_agg(boundaries)\n" .
                   "| EVAL continent_centroid = st_centroid_agg(boundaries)\n" .
                   "| SORT continent ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
