<?php

namespace EsqlPhp\Tests\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\StdDev;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class StdDevTest extends TestCase
{
    /**
     * Test basic STD_DEV function
     * Example from documentation: FROM employees | STATS STD_DEV(height)
     */
    public function testBasicStdDev(): void
    {
        $stdDev = new StdDev('height');
        $this->assertEquals('std_dev(height)', $stdDev->toString());
    }

    /**
     * Test STD_DEV with different numeric fields
     */
    public function testStdDevWithDifferentFields(): void
    {
        // Test with salary field
        $stdDev1 = new StdDev('salary');
        $this->assertEquals('std_dev(salary)', $stdDev1->toString());

        // Test with age field
        $stdDev2 = new StdDev('age');
        $this->assertEquals('std_dev(age)', $stdDev2->toString());
    }

    /**
     * Test STD_DEV in complete query from documentation example
     */
    public function testStdDevInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::stdDev('height'), 'height_stddev');

        $expected = "FROM employees\n| EVAL height_stddev = std_dev(height)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test STD_DEV with inline function
     * Example from documentation: STD_DEV(MV_MAX(salary_change))
     */
    public function testStdDevWithInlineFunction(): void
    {
        $stdDev = new StdDev('MV_MAX(salary_change)');
        $this->assertEquals('std_dev(MV_MAX(salary_change))', $stdDev->toString());
    }

    /**
     * Test STD_DEV with inline function in complete query
     * Example from documentation: FROM employees | STATS stddev_salary_change = STD_DEV(MV_MAX(salary_change))
     */
    public function testStdDevWithInlineFunctionInQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::stdDev('MV_MAX(salary_change)'), 'stddev_salary_change');

        $expected = "FROM employees\n| EVAL stddev_salary_change = std_dev(MV_MAX(salary_change))";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test STD_DEV grouped by department
     */
    public function testStdDevGroupedByDepartment(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::stdDev('salary'), 'salary_stddev')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL salary_stddev = std_dev(salary)\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
