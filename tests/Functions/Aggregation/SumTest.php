<?php

namespace EsqlPhp\Tests\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\Sum;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class SumTest extends TestCase
{
    /**
     * Test basic SUM function
     * Example from documentation: FROM employees | STATS SUM(languages)
     */
    public function testBasicSum(): void
    {
        $sum = new Sum('languages');
        $this->assertEquals('sum(languages)', $sum->toString());
    }

    /**
     * Test SUM with different numeric fields
     */
    public function testSumWithDifferentFields(): void
    {
        // Test with salary field
        $sum1 = new Sum('salary');
        $this->assertEquals('sum(salary)', $sum1->toString());

        // Test with hours_worked field
        $sum2 = new Sum('hours_worked');
        $this->assertEquals('sum(hours_worked)', $sum2->toString());
    }

    /**
     * Test SUM in complete query from documentation example
     */
    public function testSumInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::sum('languages'), 'total_languages');

        $expected = "FROM employees\n| EVAL total_languages = sum(languages)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test SUM with inline function
     * Example from documentation: SUM(MV_MAX(salary_change))
     */
    public function testSumWithInlineFunction(): void
    {
        $sum = new Sum('MV_MAX(salary_change)');
        $this->assertEquals('sum(MV_MAX(salary_change))', $sum->toString());
    }

    /**
     * Test SUM with inline function in complete query
     * Example from documentation: FROM employees | STATS total_salary_changes = SUM(MV_MAX(salary_change))
     */
    public function testSumWithInlineFunctionInQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::sum('MV_MAX(salary_change)'), 'total_salary_changes');

        $expected = "FROM employees\n| EVAL total_salary_changes = sum(MV_MAX(salary_change))";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test SUM grouped by department
     */
    public function testSumGroupedByDepartment(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::sum('salary'), 'total_salary')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL total_salary = sum(salary)\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
