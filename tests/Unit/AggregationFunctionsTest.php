<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Tests\Unit;

use PHPUnit\Framework\TestCase;
use EsqlPhp\QueryBuilder\QueryBuilder;

class AggregationFunctionsTest extends TestCase
{
    public function testCountDistinct(): void
    {
        $function = QueryBuilder::countDistinct('user_id');
        $this->assertEquals('count_distinct(user_id)', $function->toString());
        
        $functionWithPrecision = QueryBuilder::countDistinct('user_id', 1000);
        $this->assertEquals('count_distinct(user_id, 1000)', $functionWithPrecision->toString());
    }

    public function testMedian(): void
    {
        $function = QueryBuilder::median('salary');
        $this->assertEquals('median(salary)', $function->toString());
    }

    public function testMedianAbsoluteDeviation(): void
    {
        $function = QueryBuilder::medianAbsoluteDeviation('response_time');
        $this->assertEquals('median_absolute_deviation(response_time)', $function->toString());
    }

    public function testPercentile(): void
    {
        $function = QueryBuilder::percentile('salary', 95);
        $this->assertEquals('percentile(salary, 95)', $function->toString());
        
        $functionWithFloat = QueryBuilder::percentile('response_time', 99.9);
        $this->assertEquals('percentile(response_time, 99.9)', $functionWithFloat->toString());
    }

    public function testStCentroidAgg(): void
    {
        $function = QueryBuilder::stCentroidAgg('location');
        $this->assertEquals('st_centroid_agg(location)', $function->toString());
    }

    public function testStExtentAgg(): void
    {
        $function = QueryBuilder::stExtentAgg('location');
        $this->assertEquals('st_extent_agg(location)', $function->toString());
    }

    public function testStdDev(): void
    {
        $function = QueryBuilder::stdDev('height');
        $this->assertEquals('std_dev(height)', $function->toString());
    }

    public function testTop(): void
    {
        $function = QueryBuilder::top('salary', 3);
        $this->assertEquals('top(salary, 3, \'asc\')', $function->toString());
        
        $functionDesc = QueryBuilder::top('salary', 5, 'desc');
        $this->assertEquals('top(salary, 5, \'desc\')', $functionDesc->toString());
    }

    public function testValues(): void
    {
        $function = QueryBuilder::values('department');
        $this->assertEquals('values(department)', $function->toString());
    }

    public function testWeightedAvg(): void
    {
        $function = QueryBuilder::weightedAvg('salary', 'experience');
        $this->assertEquals('weighted_avg(salary, experience)', $function->toString());
        
        $functionWithNumber = QueryBuilder::weightedAvg('price', 2.5);
        $this->assertEquals('weighted_avg(price, 2.5)', $functionWithNumber->toString());
    }

    public function testAggregationInQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::countDistinct('employee_id'), 'unique_employees')
            ->eval(QueryBuilder::median('salary'), 'median_salary')
            ->eval(QueryBuilder::percentile('salary', 95), 'p95_salary')
            ->eval(QueryBuilder::stdDev('age'), 'age_stddev')
            ->eval(QueryBuilder::weightedAvg('salary', 'experience'), 'weighted_avg_salary')
            ->orderBy('median_salary', 'DESC');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL unique_employees = count_distinct(employee_id)\n" .
                   "| EVAL median_salary = median(salary)\n" .
                   "| EVAL p95_salary = percentile(salary, 95)\n" .
                   "| EVAL age_stddev = std_dev(age)\n" .
                   "| EVAL weighted_avg_salary = weighted_avg(salary, experience)\n" .
                   "| SORT median_salary DESC";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testTopAndValuesInQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('sales')
            ->groupBy(['region'])
            ->eval(QueryBuilder::top('amount', 3, 'desc'), 'top_sales')
            ->eval(QueryBuilder::values('product_category'), 'categories')
            ->orderBy('region');

        $expected = "FROM sales\n" .
                   "| GROUP BY region\n" .
                   "| EVAL top_sales = top(amount, 3, 'desc')\n" .
                   "| EVAL categories = values(product_category)\n" .
                   "| SORT region ASC";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testSpatialAggregationsInQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('airports')
            ->where('country', '=', 'France')
            ->eval(QueryBuilder::stCentroidAgg('location'), 'centroid')
            ->eval(QueryBuilder::stExtentAgg('location'), 'extent');

        $expected = "FROM airports\n" .
                   "| WHERE country = 'France'\n" .
                   "| EVAL centroid = st_centroid_agg(location)\n" .
                   "| EVAL extent = st_extent_agg(location)";

        $this->assertEquals($expected, $query->toEsql());
    }
}
