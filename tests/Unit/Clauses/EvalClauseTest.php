<?php

declare(strict_types=1);

namespace EsqlPhp\Tests\Unit\Clauses;

use EsqlPhp\QueryBuilder\Clauses\EvalClause;
use EsqlPhp\QueryBuilder\Functions\Aggregation\Avg;
use EsqlPhp\QueryBuilder\Functions\Math\Round;
use PHPUnit\Framework\TestCase;

class EvalClauseTest extends TestCase
{
    public function testSingleExpressionWithAlias(): void
    {
        $evalClause = new EvalClause();
        $evalClause->addExpression(new Avg('salary'), 'avg_salary');

        $expected = '| EVAL avg_salary = avg(salary)';
        $this->assertEquals($expected, $evalClause->toString());
    }

    public function testSingleExpressionWithoutAlias(): void
    {
        $evalClause = new EvalClause();
        $evalClause->addExpression('salary * 1.1');

        $expected = '| EVAL salary * 1.1';
        $this->assertEquals($expected, $evalClause->toString());
    }

    public function testMultipleExpressions(): void
    {
        $evalClause = new EvalClause();
        $evalClause->addExpression(new Avg('salary'), 'avg_salary');
        $evalClause->addExpression(new Round('price * 1.2', 2), 'price_with_tax');
        $evalClause->addExpression('status = "active"', 'is_active');

        $expected = "| EVAL avg_salary = avg(salary)\n" .
                   "| EVAL price_with_tax = round(price * 1.2, 2)\n" .
                   "| EVAL is_active = status = \"active\"";
        $this->assertEquals($expected, $evalClause->toString());
    }

    public function testMixedExpressionsWithAndWithoutAlias(): void
    {
        $evalClause = new EvalClause();
        $evalClause->addExpression(new Avg('salary'), 'avg_salary');
        $evalClause->addExpression('salary * 1.1');
        $evalClause->addExpression('count(*)', 'total_count');

        $expected = "| EVAL avg_salary = avg(salary)\n" .
                   "| EVAL salary * 1.1\n" .
                   "| EVAL total_count = count(*)";
        $this->assertEquals($expected, $evalClause->toString());
    }

    public function testEmptyEvalClause(): void
    {
        $evalClause = new EvalClause();

        $this->assertEquals('', $evalClause->toString());
    }

    public function testStringExpressionWithAlias(): void
    {
        $evalClause = new EvalClause();
        $evalClause->addExpression('MV_MAX(salary_change)', 'max_salary_change');

        $expected = '| EVAL max_salary_change = MV_MAX(salary_change)';
        $this->assertEquals($expected, $evalClause->toString());
    }
}
