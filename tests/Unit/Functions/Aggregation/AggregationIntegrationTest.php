<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class AggregationIntegrationTest extends TestCase
{
    /**
     * Test comprehensive employee statistics query using multiple aggregation functions
     */
    public function testComprehensiveEmployeeStatistics(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::count('*'), 'employee_count')
            ->eval(QueryBuilder::countDistinct('job_title'), 'unique_roles')
            ->eval(QueryBuilder::avg('salary'), 'avg_salary')
            ->eval(QueryBuilder::median('salary'), 'median_salary')
            ->eval(QueryBuilder::stdDev('salary'), 'salary_stddev')
            ->eval(QueryBuilder::percentile('salary', 95), 'p95_salary')
            ->eval(QueryBuilder::min('hire_date'), 'earliest_hire')
            ->eval(QueryBuilder::max('hire_date'), 'latest_hire')
            ->eval(QueryBuilder::sum('years_experience'), 'total_experience')
            ->eval(QueryBuilder::weightedAvg('performance_score', 'years_experience'), 'weighted_performance')
            ->orderBy('avg_salary', 'DESC');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL employee_count = count(*)\n" .
                   "| EVAL unique_roles = count_distinct(job_title)\n" .
                   "| EVAL avg_salary = avg(salary)\n" .
                   "| EVAL median_salary = median(salary)\n" .
                   "| EVAL salary_stddev = std_dev(salary)\n" .
                   "| EVAL p95_salary = percentile(salary, 95)\n" .
                   "| EVAL earliest_hire = min(hire_date)\n" .
                   "| EVAL latest_hire = max(hire_date)\n" .
                   "| EVAL total_experience = sum(years_experience)\n" .
                   "| EVAL weighted_performance = weighted_avg(performance_score, years_experience)\n" .
                   "| SORT avg_salary DESC";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test sales analysis with TOP and VALUES functions
     */
    public function testSalesAnalysisWithTopAndValues(): void
    {
        $query = (new QueryBuilder())
            ->from('sales')
            ->groupBy(['region', 'quarter'])
            ->eval(QueryBuilder::sum('amount'), 'total_sales')
            ->eval(QueryBuilder::top('amount', 5, 'desc'), 'top_deals')
            ->eval(QueryBuilder::values('product_category'), 'categories_sold')
            ->eval(QueryBuilder::countDistinct('customer_id'), 'unique_customers')
            ->eval(QueryBuilder::avg('deal_size'), 'avg_deal_size')
            ->orderBy('total_sales', 'DESC');

        $expected = "FROM sales\n" .
                   "| GROUP BY region, quarter\n" .
                   "| EVAL total_sales = sum(amount)\n" .
                   "| EVAL top_deals = top(amount, 5, 'desc')\n" .
                   "| EVAL categories_sold = values(product_category)\n" .
                   "| EVAL unique_customers = count_distinct(customer_id)\n" .
                   "| EVAL avg_deal_size = avg(deal_size)\n" .
                   "| SORT total_sales DESC";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test geographic analysis with spatial aggregation functions
     */
    public function testGeographicAnalysisWithSpatialFunctions(): void
    {
        $query = (new QueryBuilder())
            ->from('stores')
            ->where('active', '=', true)
            ->groupBy(['city'])
            ->eval(QueryBuilder::count('*'), 'store_count')
            ->eval(QueryBuilder::stCentroidAgg('location'), 'city_center')
            ->eval(QueryBuilder::stExtentAgg('location'), 'coverage_area')
            ->eval(QueryBuilder::avg('revenue'), 'avg_revenue')
            ->eval(QueryBuilder::sum('employees'), 'total_employees')
            ->orderBy('store_count', 'DESC');

        $expected = "FROM stores\n" .
                   "| WHERE active = true\n" .
                   "| GROUP BY city\n" .
                   "| EVAL store_count = count(*)\n" .
                   "| EVAL city_center = st_centroid_agg(location)\n" .
                   "| EVAL coverage_area = st_extent_agg(location)\n" .
                   "| EVAL avg_revenue = avg(revenue)\n" .
                   "| EVAL total_employees = sum(employees)\n" .
                   "| SORT store_count DESC";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test performance metrics with percentiles and deviation analysis
     */
    public function testPerformanceMetricsAnalysis(): void
    {
        $query = (new QueryBuilder())
            ->from('api_requests')
            ->where('status_code', '=', 200)
            ->groupBy(['endpoint'])
            ->eval(QueryBuilder::count('*'), 'request_count')
            ->eval(QueryBuilder::avg('response_time'), 'avg_response_time')
            ->eval(QueryBuilder::median('response_time'), 'median_response_time')
            ->eval(QueryBuilder::percentile('response_time', 95), 'p95_response_time')
            ->eval(QueryBuilder::percentile('response_time', 99), 'p99_response_time')
            ->eval(QueryBuilder::stdDev('response_time'), 'response_time_stddev')
            ->eval(QueryBuilder::medianAbsoluteDeviation('response_time'), 'response_time_mad')
            ->eval(QueryBuilder::min('response_time'), 'min_response_time')
            ->eval(QueryBuilder::max('response_time'), 'max_response_time')
            ->orderBy('p95_response_time', 'DESC');

        $expected = "FROM api_requests\n" .
                   "| WHERE status_code = 200\n" .
                   "| GROUP BY endpoint\n" .
                   "| EVAL request_count = count(*)\n" .
                   "| EVAL avg_response_time = avg(response_time)\n" .
                   "| EVAL median_response_time = median(response_time)\n" .
                   "| EVAL p95_response_time = percentile(response_time, 95)\n" .
                   "| EVAL p99_response_time = percentile(response_time, 99)\n" .
                   "| EVAL response_time_stddev = std_dev(response_time)\n" .
                   "| EVAL response_time_mad = median_absolute_deviation(response_time)\n" .
                   "| EVAL min_response_time = min(response_time)\n" .
                   "| EVAL max_response_time = max(response_time)\n" .
                   "| SORT p95_response_time DESC";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test complex query with inline functions and multiple aggregations
     */
    public function testComplexQueryWithInlineFunctions(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::avg('MV_MAX(salary_change)'), 'avg_max_salary_change')
            ->eval(QueryBuilder::sum('MV_MAX(salary_change)'), 'total_salary_changes')
            ->eval(QueryBuilder::stdDev('MV_MAX(salary_change)'), 'stddev_salary_change')
            ->eval('MV_SORT(VALUES(job_title))', 'job_titles')
            ->eval(QueryBuilder::weightedAvg('salary', 'MV_MAX(performance_ratings)'), 'weighted_salary')
            ->orderBy('avg_max_salary_change', 'DESC');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL avg_max_salary_change = avg(MV_MAX(salary_change))\n" .
                   "| EVAL total_salary_changes = sum(MV_MAX(salary_change))\n" .
                   "| EVAL stddev_salary_change = std_dev(MV_MAX(salary_change))\n" .
                   "| EVAL job_titles = MV_SORT(VALUES(job_title))\n" .
                   "| EVAL weighted_salary = weighted_avg(salary, MV_MAX(performance_ratings))\n" .
                   "| SORT avg_max_salary_change DESC";

        $this->assertEquals($expected, $query->toEsql());
    }
}
