<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\Avg;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class AvgTest extends TestCase
{
    /**
     * Test basic AVG function
     * Example from documentation: FROM employees | STATS AVG(height)
     */
    public function testBasicAvg(): void
    {
        $avg = new Avg('height');
        $this->assertEquals('avg(height)', $avg->toString());
    }

    /**
     * Test AVG function with inline function
     * Example from documentation: FROM employees | STATS avg_salary_change = AVG(MV_MAX(salary_change))
     */
    public function testAvgWithInlineFunction(): void
    {
        $avg = new Avg('MV_MAX(salary_change)');
        $this->assertEquals('avg(MV_MAX(salary_change))', $avg->toString());
    }

    /**
     * Test AVG in complete query from documentation example
     */
    public function testAvgInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::avg('height'), 'avg_height');

        $expected = "FROM employees\n| EVAL avg_height = avg(height)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test AVG with inline function in complete query
     */
    public function testAvgWithInlineFunctionInQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::avg('MV_MAX(salary_change)'), 'avg_salary_change');

        $expected = "FROM employees\n| EVAL avg_salary_change = avg(MV_MAX(salary_change))";
        $this->assertEquals($expected, $query->toEsql());
    }
}
