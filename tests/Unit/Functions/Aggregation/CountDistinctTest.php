<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\CountDistinct;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class CountDistinctTest extends TestCase
{
    /**
     * Test basic COUNT_DISTINCT function
     * Example from documentation: FROM employees | STATS COUNT_DISTINCT(languages)
     */
    public function testBasicCountDistinct(): void
    {
        $countDistinct = new CountDistinct('languages');
        $this->assertEquals('count_distinct(languages)', $countDistinct->toString());
    }

    /**
     * Test COUNT_DISTINCT with precision parameter
     * Example from documentation: FROM employees | STATS COUNT_DISTINCT(languages, 1000)
     */
    public function testCountDistinctWithPrecision(): void
    {
        $countDistinct = new CountDistinct('languages', 1000);
        $this->assertEquals('count_distinct(languages, 1000)', $countDistinct->toString());
    }

    /**
     * Test COUNT_DISTINCT in complete query from documentation example
     */
    public function testCountDistinctInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::countDistinct('languages'), 'unique_languages');

        $expected = "FROM employees\n| EVAL unique_languages = count_distinct(languages)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test COUNT_DISTINCT with precision in complete query
     */
    public function testCountDistinctWithPrecisionInQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::countDistinct('languages', 1000), 'unique_languages');

        $expected = "FROM employees\n| EVAL unique_languages = count_distinct(languages, 1000)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test COUNT_DISTINCT with different field types
     */
    public function testCountDistinctWithDifferentFields(): void
    {
        // Test with employee_id field
        $countDistinct1 = new CountDistinct('employee_id');
        $this->assertEquals('count_distinct(employee_id)', $countDistinct1->toString());

        // Test with department field
        $countDistinct2 = new CountDistinct('department');
        $this->assertEquals('count_distinct(department)', $countDistinct2->toString());
    }
}
