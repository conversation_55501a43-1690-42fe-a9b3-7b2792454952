<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\MedianAbsoluteDeviation;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class MedianAbsoluteDeviationTest extends TestCase
{
    /**
     * Test basic MEDIAN_ABSOLUTE_DEVIATION function
     * Example from documentation: FROM employees | STATS MEDIAN_ABSOLUTE_DEVIATION(salary)
     */
    public function testBasicMedianAbsoluteDeviation(): void
    {
        $mad = new MedianAbsoluteDeviation('salary');
        $this->assertEquals('median_absolute_deviation(salary)', $mad->toString());
    }

    /**
     * Test MEDIAN_ABSOLUTE_DEVIATION with different numeric fields
     */
    public function testMedianAbsoluteDeviationWithDifferentFields(): void
    {
        // Test with height field
        $mad1 = new MedianAbsoluteDeviation('height');
        $this->assertEquals('median_absolute_deviation(height)', $mad1->toString());

        // Test with age field
        $mad2 = new MedianAbsoluteDeviation('age');
        $this->assertEquals('median_absolute_deviation(age)', $mad2->toString());
    }

    /**
     * Test MEDIAN_ABSOLUTE_DEVIATION in complete query from documentation example
     */
    public function testMedianAbsoluteDeviationInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::medianAbsoluteDeviation('salary'), 'mad_salary');

        $expected = "FROM employees\n| EVAL mad_salary = median_absolute_deviation(salary)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test MEDIAN_ABSOLUTE_DEVIATION with inline function
     */
    public function testMedianAbsoluteDeviationWithInlineFunction(): void
    {
        $mad = new MedianAbsoluteDeviation('MV_MAX(salary_change)');
        $this->assertEquals('median_absolute_deviation(MV_MAX(salary_change))', $mad->toString());
    }

    /**
     * Test MEDIAN_ABSOLUTE_DEVIATION grouped by department
     */
    public function testMedianAbsoluteDeviationGroupedByDepartment(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::medianAbsoluteDeviation('salary'), 'mad_salary')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL mad_salary = median_absolute_deviation(salary)\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
