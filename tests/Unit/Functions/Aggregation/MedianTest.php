<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\Median;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class MedianTest extends TestCase
{
    /**
     * Test basic MEDIAN function
     * Example from documentation: FROM employees | STATS MEDIAN(salary)
     */
    public function testBasicMedian(): void
    {
        $median = new Median('salary');
        $this->assertEquals('median(salary)', $median->toString());
    }

    /**
     * Test MEDIAN with different numeric fields
     */
    public function testMedianWithDifferentFields(): void
    {
        // Test with height field
        $median1 = new Median('height');
        $this->assertEquals('median(height)', $median1->toString());

        // Test with age field
        $median2 = new Median('age');
        $this->assertEquals('median(age)', $median2->toString());
    }

    /**
     * Test MEDIAN in complete query from documentation example
     */
    public function testMedianInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::median('salary'), 'median_salary');

        $expected = "FROM employees\n| EVAL median_salary = median(salary)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test MEDIAN with inline function
     * Example: MEDIAN(MV_MAX(salary_change))
     */
    public function testMedianWithInlineFunction(): void
    {
        $median = new Median('MV_MAX(salary_change)');
        $this->assertEquals('median(MV_MAX(salary_change))', $median->toString());
    }

    /**
     * Test MEDIAN grouped by department
     */
    public function testMedianGroupedByDepartment(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::median('salary'), 'median_salary')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL median_salary = median(salary)\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
