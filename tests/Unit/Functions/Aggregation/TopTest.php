<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\Top;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class TopTest extends TestCase
{
    /**
     * Test basic TOP function with default order (asc)
     * Example from documentation: FROM employees | STATS top_salaries = TOP(salary, 3, "desc")
     */
    public function testBasicTop(): void
    {
        $top = new Top('salary', 3);
        $this->assertEquals('top(salary, 3, \'asc\')', $top->toString());
    }

    /**
     * Test TOP function with desc order
     * Example from documentation: FROM employees | STATS top_salaries = TOP(salary, 3, "desc")
     */
    public function testTopWithDescOrder(): void
    {
        $top = new Top('salary', 3, "desc");
        $this->assertEquals('top(salary, 3, \'desc\')', $top->toString());
    }

    /**
     * Test TOP function with different limits
     */
    public function testTopWithDifferentLimits(): void
    {
        // Test with limit 5
        $top1 = new Top('score', 5, 'desc');
        $this->assertEquals('top(score, 5, \'desc\')', $top1->toString());

        // Test with limit 10
        $top2 = new Top('rating', 10, 'asc');
        $this->assertEquals('top(rating, 10, \'asc\')', $top2->toString());
    }

    /**
     * Test TOP in complete query from documentation example
     * FROM employees | STATS top_salaries = TOP(salary, 3, "desc"), top_salary = MAX(salary)
     */
    public function testTopInCompleteQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::top('salary', 3, 'desc'), 'top_salaries')
            ->eval(QueryBuilder::max('salary'), 'top_salary');

        $expected = "FROM employees\n" .
                   "| EVAL top_salaries = top(salary, 3, 'desc')\n" .
                   "| EVAL top_salary = max(salary)";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test TOP with different field types
     */
    public function testTopWithDifferentFields(): void
    {
        // Test with string field
        $top1 = new Top('name', 5, 'asc');
        $this->assertEquals('top(name, 5, \'asc\')', $top1->toString());

        // Test with date field
        $top2 = new Top('hire_date', 3, 'desc');
        $this->assertEquals('top(hire_date, 3, \'desc\')', $top2->toString());
    }

    /**
     * Test TOP grouped by department
     */
    public function testTopGroupedByDepartment(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::top('salary', 2, 'desc'), 'top_salaries')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL top_salaries = top(salary, 2, 'desc')\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
