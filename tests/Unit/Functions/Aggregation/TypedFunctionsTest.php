<?php

declare(strict_types=1);

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedTop;
use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedPercentile;
use EsqlPhp\QueryBuilder\Functions\Aggregation\TypedCountDistinct;
use PHPUnit\Framework\TestCase;
use InvalidArgumentException;

class TypedFunctionsTest extends TestCase
{
    public function testTypedTopWithValidArguments(): void
    {
        // Test with default order (asc)
        $top1 = new TypedTop('salary', 3);
        $this->assertEquals("top(salary, 3, 'asc')", $top1->toString());

        // Test with explicit desc order
        $top2 = new TypedTop('salary', 5, 'desc');
        $this->assertEquals("top(salary, 5, 'desc')", $top2->toString());

        // Test with case insensitive order
        $top3 = new TypedTop('salary', 2, 'DESC');
        $this->assertEquals("top(salary, 2, 'desc')", $top3->toString());

        // Test with field expression
        $top4 = new TypedTop('MV_MAX(scores)', 10, 'asc');
        $this->assertEquals("top(MV_MAX(scores), 10, 'asc')", $top4->toString());
    }

    public function testTypedTopWithInvalidOrder(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid value "invalid". Allowed values are: asc, desc');
        new TypedTop('salary', 3, 'invalid');
    }

    public function testTypedTopWithInvalidLimit(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be positive');
        new TypedTop('salary', 0);
    }

    public function testTypedTopWithNegativeLimit(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be at least 1');
        new TypedTop('salary', -1);
    }

    public function testTypedPercentileWithValidArguments(): void
    {
        // Test with integer percentile
        $p1 = new TypedPercentile('salary', 95);
        $this->assertEquals('percentile(salary, 95)', $p1->toString());

        // Test with float percentile
        $p2 = new TypedPercentile('response_time', 99.9);
        $this->assertEquals('percentile(response_time, 99.9)', $p2->toString());

        // Test with field expression
        $p3 = new TypedPercentile('MV_AVG(scores)', 50);
        $this->assertEquals('percentile(MV_AVG(scores), 50)', $p3->toString());

        // Test with boundary values
        $p4 = new TypedPercentile('data', 0);
        $this->assertEquals('percentile(data, 0)', $p4->toString());

        $p5 = new TypedPercentile('data', 100);
        $this->assertEquals('percentile(data, 100)', $p5->toString());
    }

    public function testTypedPercentileWithInvalidPercentile(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be at most 100');
        new TypedPercentile('salary', 101);
    }

    public function testTypedPercentileWithNegativePercentile(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be non-negative');
        new TypedPercentile('salary', -1);
    }

    public function testTypedCountDistinctWithValidArguments(): void
    {
        // Test without precision
        $cd1 = new TypedCountDistinct('user_id');
        $this->assertEquals('count_distinct(user_id)', $cd1->toString());

        // Test with precision
        $cd2 = new TypedCountDistinct('user_id', 1000);
        $this->assertEquals('count_distinct(user_id, 1000)', $cd2->toString());

        // Test with field expression
        $cd3 = new TypedCountDistinct('MV_DEDUPE(tags)', 500);
        $this->assertEquals('count_distinct(MV_DEDUPE(tags), 500)', $cd3->toString());
    }

    public function testTypedCountDistinctWithInvalidPrecision(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be positive');
        new TypedCountDistinct('user_id', 0);
    }

    public function testTypedCountDistinctWithNegativePrecision(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be at least 1');
        new TypedCountDistinct('user_id', -100);
    }

    public function testTypedFunctionsWithComplexFieldExpressions(): void
    {
        // Test with math expressions
        $top1 = new TypedTop('salary * 1.2', 5, 'desc');
        $this->assertEquals("top(salary * 1.2, 5, 'desc')", $top1->toString());

        // Test with nested function calls
        $p1 = new TypedPercentile('ROUND(MV_AVG(scores), 2)', 90);
        $this->assertEquals('percentile(ROUND(MV_AVG(scores), 2), 90)', $p1->toString());

        // Test with asterisk
        $cd1 = new TypedCountDistinct('*');
        $this->assertEquals('count_distinct(*)', $cd1->toString());
    }

    public function testArgumentTypeIntrospection(): void
    {
        $top = new TypedTop('salary', 3, 'desc');
        $argumentTypes = $top->getArgumentTypes();
        
        $this->assertCount(3, $argumentTypes);
        $this->assertInstanceOf(\EsqlPhp\QueryBuilder\Functions\ArgumentTypes\FieldArgument::class, $argumentTypes[0]);
        $this->assertInstanceOf(\EsqlPhp\QueryBuilder\Functions\ArgumentTypes\NumericArgument::class, $argumentTypes[1]);
        $this->assertInstanceOf(\EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument::class, $argumentTypes[2]);
    }
}
