<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\Values;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class ValuesTest extends TestCase
{
    /**
     * Test basic VALUES function
     * Example from documentation: FROM employees | EVAL first_letter = SUBSTRING(first_name, 0, 1) | STATS first_name = MV_SORT(VALUES(first_name)) BY first_letter
     */
    public function testBasicValues(): void
    {
        $values = new Values('first_name');
        $this->assertEquals('values(first_name)', $values->toString());
    }

    /**
     * Test VALUES with different field types
     */
    public function testValuesWithDifferentFields(): void
    {
        // Test with department field
        $values1 = new Values('department');
        $this->assertEquals('values(department)', $values1->toString());

        // Test with skill field
        $values2 = new Values('skills');
        $this->assertEquals('values(skills)', $values2->toString());
    }

    /**
     * Test VALUES in complete query from documentation example
     * FROM employees | EVAL first_letter = SUBSTRING(first_name, 0, 1) | STATS first_name = MV_SORT(VALUES(first_name)) BY first_letter | SORT first_letter
     */
    public function testValuesInCompleteQueryFromDocumentation(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->eval(QueryBuilder::substring('first_name', 0, 1), 'first_letter')
            ->groupBy(['first_letter'])
            ->eval('MV_SORT(VALUES(first_name))', 'first_name')
            ->orderBy('first_letter');

        $expected = "FROM employees\n" .
                   "| EVAL first_letter = substring(first_name, 0, 1)\n" .
                   "| GROUP BY first_letter\n" .
                   "| EVAL first_name = MV_SORT(VALUES(first_name))\n" .
                   "| SORT first_letter ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test VALUES with MV_SORT wrapper
     */
    public function testValuesWithMvSort(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval('MV_SORT(VALUES(first_name))', 'sorted_names')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL sorted_names = MV_SORT(VALUES(first_name))\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test VALUES simple usage
     */
    public function testValuesSimpleUsage(): void
    {
        $query = (new QueryBuilder())
            ->from('products')
            ->groupBy(['category'])
            ->eval(QueryBuilder::values('brand'), 'unique_brands')
            ->orderBy('category');

        $expected = "FROM products\n" .
                   "| GROUP BY category\n" .
                   "| EVAL unique_brands = values(brand)\n" .
                   "| SORT category ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test VALUES with multiple fields
     */
    public function testValuesWithMultipleFields(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::values('job_title'), 'job_titles')
            ->eval(QueryBuilder::values('location'), 'locations')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL job_titles = values(job_title)\n" .
                   "| EVAL locations = values(location)\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }
}
