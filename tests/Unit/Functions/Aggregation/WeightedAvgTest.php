<?php

namespace EsqlPhp\Tests\Unit\Functions\Aggregation;

use EsqlPhp\QueryBuilder\Functions\Aggregation\WeightedAvg;
use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class WeightedAvgTest extends TestCase
{
    /**
     * Test basic WEIGHTED_AVG function
     * Example from documentation: FROM employees | STATS w_avg = WEIGHTED_AVG(salary, height) BY languages
     */
    public function testBasicWeightedAvg(): void
    {
        $weightedAvg = new WeightedAvg('salary', 'height');
        $this->assertEquals('weighted_avg(salary, height)', $weightedAvg->toString());
    }

    /**
     * Test WEIGHTED_AVG with numeric weight
     */
    public function testWeightedAvgWithNumericWeight(): void
    {
        $weightedAvg = new WeightedAvg('price', 2.5);
        $this->assertEquals('weighted_avg(price, 2.5)', $weightedAvg->toString());
    }

    /**
     * Test WEIGHTED_AVG with integer weight
     */
    public function testWeightedAvgWithIntegerWeight(): void
    {
        $weightedAvg = new WeightedAvg('score', 3);
        $this->assertEquals('weighted_avg(score, 3)', $weightedAvg->toString());
    }

    /**
     * Test WEIGHTED_AVG in complete query from documentation example
     * FROM employees | STATS w_avg = WEIGHTED_AVG(salary, height) BY languages | EVAL w_avg = ROUND(w_avg) | KEEP w_avg, languages | SORT languages
     */
    public function testWeightedAvgInCompleteQueryFromDocumentation(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['languages'])
            ->eval(QueryBuilder::weightedAvg('salary', 'height'), 'w_avg')
            ->eval(QueryBuilder::round('w_avg'), 'w_avg')
            ->keep(['w_avg', 'languages'])
            ->orderBy('languages');

        $expected = "FROM employees\n" .
                   "| GROUP BY languages\n" .
                   "| EVAL w_avg = weighted_avg(salary, height)\n" .
                   "| EVAL w_avg = round(w_avg)\n" .
                   "| KEEP w_avg, languages\n" .
                   "| SORT languages ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test WEIGHTED_AVG with different field combinations
     */
    public function testWeightedAvgWithDifferentFields(): void
    {
        // Test with performance and experience
        $weightedAvg1 = new WeightedAvg('performance_score', 'years_experience');
        $this->assertEquals('weighted_avg(performance_score, years_experience)', $weightedAvg1->toString());

        // Test with revenue and market_share
        $weightedAvg2 = new WeightedAvg('revenue', 'market_share');
        $this->assertEquals('weighted_avg(revenue, market_share)', $weightedAvg2->toString());
    }

    /**
     * Test WEIGHTED_AVG grouped by department
     */
    public function testWeightedAvgGroupedByDepartment(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::weightedAvg('salary', 'performance_rating'), 'weighted_avg_salary')
            ->orderBy('department');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL weighted_avg_salary = weighted_avg(salary, performance_rating)\n" .
                   "| SORT department ASC";
        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test WEIGHTED_AVG with ROUND function as in documentation
     */
    public function testWeightedAvgWithRound(): void
    {
        $query = (new QueryBuilder())
            ->from('sales')
            ->groupBy(['region'])
            ->eval(QueryBuilder::weightedAvg('amount', 'quantity'), 'w_avg')
            ->eval(QueryBuilder::round('w_avg', 2), 'rounded_w_avg');

        $expected = "FROM sales\n" .
                   "| GROUP BY region\n" .
                   "| EVAL w_avg = weighted_avg(amount, quantity)\n" .
                   "| EVAL rounded_w_avg = round(w_avg, 2)";
        $this->assertEquals($expected, $query->toEsql());
    }
}
