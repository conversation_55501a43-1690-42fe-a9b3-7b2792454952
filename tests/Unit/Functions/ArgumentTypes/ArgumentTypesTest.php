<?php

declare(strict_types=1);

namespace EsqlPhp\Tests\Unit\Functions\ArgumentTypes;

use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\FieldArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\StringArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\NumericArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\EnumArgument;
use EsqlPhp\QueryBuilder\Functions\ArgumentTypes\OptionalArgument;
use PHPUnit\Framework\TestCase;
use InvalidArgumentException;

class ArgumentTypesTest extends TestCase
{
    public function testFieldArgumentFormatting(): void
    {
        // Field reference - not quoted
        $field1 = new FieldArgument('salary');
        $this->assertEquals('salary', $field1->format());

        // Nested field reference - not quoted
        $field2 = new FieldArgument('employee.salary');
        $this->assertEquals('employee.salary', $field2->format());

        // Function call - not quoted
        $field3 = new FieldArgument('MV_MAX(salary_change)');
        $this->assertEquals('MV_MAX(salary_change)', $field3->format());

        // Math expression - not quoted
        $field4 = new FieldArgument('salary * 1.2');
        $this->assertEquals('salary * 1.2', $field4->format());

        // Asterisk - not quoted
        $field5 = new FieldArgument('*');
        $this->assertEquals('*', $field5->format());
    }

    public function testStringArgumentFormatting(): void
    {
        // Simple string - quoted
        $str1 = new StringArgument('hello');
        $this->assertEquals("'hello'", $str1->format());

        // String with single quote - escaped and quoted
        $str2 = new StringArgument("it's working");
        $this->assertEquals("'it''s working'", $str2->format());

        // Already quoted string - left as-is
        $str3 = new StringArgument("'already quoted'");
        $this->assertEquals("'already quoted'", $str3->format());
    }

    public function testNumericArgumentFormatting(): void
    {
        // Integer
        $num1 = new NumericArgument(42);
        $this->assertEquals('42', $num1->format());

        // Float
        $num2 = new NumericArgument(3.14);
        $this->assertEquals('3.14', $num2->format());

        // String number
        $num3 = new NumericArgument('123');
        $this->assertEquals('123', $num3->format());
    }

    public function testEnumArgumentValidation(): void
    {
        // Valid enum value
        $enum1 = new EnumArgument('asc', ['asc', 'desc']);
        $this->assertEquals("'asc'", $enum1->format());

        // Case insensitive enum
        $enum2 = new EnumArgument('ASC', ['asc', 'desc'], false);
        $this->assertEquals("'asc'", $enum2->format());

        // Invalid enum value should throw exception
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid value "invalid". Allowed values are: asc, desc');
        new EnumArgument('invalid', ['asc', 'desc']);
    }

    public function testNumericArgumentValidation(): void
    {
        // Valid positive integer
        $num1 = new NumericArgument(5, ['integer' => true, 'positive' => true]);
        $this->assertEquals('5', $num1->format());

        // Invalid: negative number when positive required
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be positive');
        new NumericArgument(-1, ['positive' => true]);
    }

    public function testNumericArgumentMinMaxValidation(): void
    {
        // Valid value within range
        $num1 = new NumericArgument(50, ['min' => 0, 'max' => 100]);
        $this->assertEquals('50', $num1->format());

        // Invalid: below minimum
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value must be at least 0');
        new NumericArgument(-5, ['min' => 0, 'max' => 100]);
    }

    public function testOptionalArgumentWithValue(): void
    {
        $stringArg = new StringArgument('test');
        $optional = new OptionalArgument($stringArg);
        
        $this->assertTrue($optional->hasValue());
        $this->assertEquals("'test'", $optional->format());
    }

    public function testOptionalArgumentWithoutValue(): void
    {
        $stringArg = new StringArgument('');
        $optional = new OptionalArgument($stringArg, 'default');
        
        // Simulate null value
        $optionalNull = new OptionalArgument(new StringArgument(''), 'default');
        $reflection = new \ReflectionProperty($optionalNull, 'value');
        $reflection->setAccessible(true);
        $reflection->setValue($optionalNull, null);
        
        $this->assertFalse($optionalNull->hasValue());
        $this->assertEquals('default', $optionalNull->format());
    }

    public function testStringArgumentWithConstraints(): void
    {
        // Valid string with allowed values
        $str1 = new StringArgument('option1', ['allowed_values' => ['option1', 'option2']]);
        $this->assertEquals("'option1'", $str1->format());

        // Invalid string with allowed values constraint
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid value "invalid". Allowed values are: option1, option2');
        new StringArgument('invalid', ['allowed_values' => ['option1', 'option2']]);
    }

    public function testStringArgumentWithPatternConstraint(): void
    {
        // Valid string matching pattern
        $str1 = new StringArgument('2024-01-01', ['pattern' => '/^\d{4}-\d{2}-\d{2}$/']);
        $this->assertEquals("'2024-01-01'", $str1->format());

        // Invalid string not matching pattern
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value "invalid-date" does not match required pattern: /^\d{4}-\d{2}-\d{2}$/');
        new StringArgument('invalid-date', ['pattern' => '/^\d{4}-\d{2}-\d{2}$/']);
    }

    public function testStringArgumentWithLengthConstraints(): void
    {
        // Valid string within length constraints
        $str1 = new StringArgument('hello', ['min_length' => 3, 'max_length' => 10]);
        $this->assertEquals("'hello'", $str1->format());

        // Invalid: too short
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('String must be at least 5 characters long');
        new StringArgument('hi', ['min_length' => 5]);
    }
}
