<?php

declare(strict_types=1);

namespace EsqlPhp\Tests\Unit\Functions;

use EsqlPhp\QueryBuilder\Functions\String\Length;
use EsqlPhp\QueryBuilder\Functions\String\LowerCase;
use EsqlPhp\QueryBuilder\Functions\Date\DateFormat;
use PHPUnit\Framework\TestCase;

class FieldReferenceTest extends TestCase
{
    public function testSimpleFieldReference(): void
    {
        $length = new Length('field_name');
        $this->assertEquals('length(field_name)', $length->toString());
    }

    public function testFieldReferenceWithUnderscore(): void
    {
        $length = new Length('field_name');
        $this->assertEquals('length(field_name)', $length->toString());
    }

    public function testFieldReferenceStartingWithAt(): void
    {
        $length = new Length('@timestamp');
        $this->assertEquals('length(@timestamp)', $length->toString());
    }

    public function testFieldReferenceWithDot(): void
    {
        $length = new Length('user.name');
        $this->assertEquals('length(user.name)', $length->toString());
    }

    public function testFieldReferenceWithSpecialCharacters(): void
    {
        $length = new Length('1.field');
        $this->assertEquals('length(`1.field`)', $length->toString());
    }

    public function testFieldReferenceWithHyphen(): void
    {
        $length = new Length('field-name');
        $this->assertEquals('length(`field-name`)', $length->toString());
    }

    public function testFieldReferenceWithSpace(): void
    {
        $length = new Length('field name');
        $this->assertEquals('length(`field name`)', $length->toString());
    }

    public function testNestedFieldWithSpecialCharacters(): void
    {
        $length = new Length('user.first-name');
        $this->assertEquals('length(user.`first-name`)', $length->toString());
    }

    public function testNestedFieldBothPartsWithSpecialCharacters(): void
    {
        $length = new Length('user-data.first-name');
        $this->assertEquals('length(`user-data`.`first-name`)', $length->toString());
    }

    public function testFieldReferenceAlreadyWithBackticks(): void
    {
        $length = new Length('`field-name`');
        $this->assertEquals('length(`field-name`)', $length->toString());
    }

    public function testNestedFieldAlreadyWithBackticks(): void
    {
        $length = new Length('user.`first-name`');
        $this->assertEquals('length(user.`first-name`)', $length->toString());
    }

    public function testStringLiteralNotFieldReference(): void
    {
        $length = new Length('Hello World');
        $this->assertEquals("length('Hello World')", $length->toString());
    }

    public function testStringLiteralWithSpecialCharacters(): void
    {
        $lowercase = new LowerCase('HELLO-WORLD');
        $this->assertEquals("lowercase('HELLO-WORLD')", $lowercase->toString());
    }

    public function testDateFormatWithFieldReference(): void
    {
        $dateFormat = new DateFormat('@timestamp', 'yyyy-MM-dd');
        $this->assertEquals("date_format(@timestamp, 'yyyy-MM-dd')", $dateFormat->toString());
    }

    public function testDateFormatWithSpecialFieldReference(): void
    {
        $dateFormat = new DateFormat('event.time-stamp', 'yyyy-MM-dd');
        $this->assertEquals("date_format(event.`time-stamp`, 'yyyy-MM-dd')", $dateFormat->toString());
    }

    public function testComplexNestedField(): void
    {
        $length = new Length('event.user-data.first-name');
        $this->assertEquals('length(event.`user-data`.`first-name`)', $length->toString());
    }

    public function testFieldStartingWithNumber(): void
    {
        $length = new Length('2fa_enabled');
        $this->assertEquals('length(`2fa_enabled`)', $length->toString());
    }

    public function testFieldWithDollarSign(): void
    {
        $length = new Length('field$name');
        $this->assertEquals('length(`field$name`)', $length->toString());
    }

    public function testFieldWithMultipleDots(): void
    {
        $length = new Length('a.b.c.d');
        $this->assertEquals('length(a.b.c.d)', $length->toString());
    }

    public function testFieldWithMixedSpecialCharacters(): void
    {
        $length = new Length('event.user-data.2fa_enabled');
        $this->assertEquals('length(event.`user-data`.`2fa_enabled`)', $length->toString());
    }
}
