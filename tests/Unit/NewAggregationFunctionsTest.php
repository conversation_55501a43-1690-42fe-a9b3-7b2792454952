<?php

declare(strict_types=1);

namespace EsqlPhp\Tests\Unit;

use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class NewAggregationFunctionsTest extends TestCase
{
    /**
     * Test que toutes les nouvelles fonctions d'agrégation génèrent la syntaxe EVAL correcte
     */
    public function testAllNewAggregationFunctionsWithCorrectEvalSyntax(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::countDistinct('job_title'), 'unique_roles')
            ->eval(QueryBuilder::median('salary'), 'median_salary')
            ->eval(QueryBuilder::percentile('salary', 95), 'p95_salary')
            ->eval(QueryBuilder::stdDev('salary'), 'salary_stddev')
            ->eval(QueryBuilder::weightedAvg('salary', 'experience'), 'weighted_avg_salary')
            ->eval(QueryBuilder::top('salary', 3, 'desc'), 'top_salaries')
            ->eval(QueryBuilder::values('skills'), 'unique_skills')
            ->orderBy('median_salary', 'DESC');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL unique_roles = count_distinct(job_title)\n" .
                   "| EVAL median_salary = median(salary)\n" .
                   "| EVAL p95_salary = percentile(salary, 95)\n" .
                   "| EVAL salary_stddev = std_dev(salary)\n" .
                   "| EVAL weighted_avg_salary = weighted_avg(salary, experience)\n" .
                   "| EVAL top_salaries = top(salary, 3, 'desc')\n" .
                   "| EVAL unique_skills = values(skills)\n" .
                   "| SORT median_salary DESC";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test des fonctions d'agrégation spatiales
     */
    public function testSpatialAggregationFunctions(): void
    {
        $query = (new QueryBuilder())
            ->from('airports')
            ->where('country', '=', 'France')
            ->eval(QueryBuilder::stCentroidAgg('location'), 'centroid')
            ->eval(QueryBuilder::stExtentAgg('location'), 'extent');

        $expected = "FROM airports\n" .
                   "| WHERE country = 'France'\n" .
                   "| EVAL centroid = st_centroid_agg(location)\n" .
                   "| EVAL extent = st_extent_agg(location)";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test des fonctions d'agrégation avec paramètres optionnels
     */
    public function testAggregationFunctionsWithOptionalParameters(): void
    {
        $query = (new QueryBuilder())
            ->from('data')
            ->eval(QueryBuilder::countDistinct('user_id'), 'unique_users_default')
            ->eval(QueryBuilder::countDistinct('user_id', 1000), 'unique_users_precise')
            ->eval(QueryBuilder::top('score', 5), 'top_scores_asc')
            ->eval(QueryBuilder::top('score', 5, 'desc'), 'top_scores_desc');

        $expected = "FROM data\n" .
                   "| EVAL unique_users_default = count_distinct(user_id)\n" .
                   "| EVAL unique_users_precise = count_distinct(user_id, 1000)\n" .
                   "| EVAL top_scores_asc = top(score, 5, 'asc')\n" .
                   "| EVAL top_scores_desc = top(score, 5, 'desc')";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test des fonctions d'agrégation avec des expressions inline
     */
    public function testAggregationFunctionsWithInlineExpressions(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::avg('MV_MAX(salary_change)'), 'avg_max_salary_change')
            ->eval(QueryBuilder::stdDev('MV_MAX(salary_change)'), 'stddev_salary_change')
            ->eval(QueryBuilder::median('MV_AVG(performance_scores)'), 'median_avg_performance')
            ->eval(QueryBuilder::weightedAvg('salary', 'MV_MAX(ratings)'), 'weighted_salary');

        $expected = "FROM employees\n" .
                   "| GROUP BY department\n" .
                   "| EVAL avg_max_salary_change = avg(MV_MAX(salary_change))\n" .
                   "| EVAL stddev_salary_change = std_dev(MV_MAX(salary_change))\n" .
                   "| EVAL median_avg_performance = median(MV_AVG(performance_scores))\n" .
                   "| EVAL weighted_salary = weighted_avg(salary, MV_MAX(ratings))";

        $this->assertEquals($expected, $query->toEsql());
    }

    /**
     * Test d'une requête d'analyse statistique complète
     */
    public function testCompleteStatisticalAnalysisQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('sales_data')
            ->where('date', '>=', '2024-01-01')
            ->groupBy(['region', 'product_category'])
            ->eval(QueryBuilder::count('*'), 'total_sales')
            ->eval(QueryBuilder::sum('amount'), 'total_revenue')
            ->eval(QueryBuilder::avg('amount'), 'avg_sale_amount')
            ->eval(QueryBuilder::median('amount'), 'median_sale_amount')
            ->eval(QueryBuilder::stdDev('amount'), 'amount_stddev')
            ->eval(QueryBuilder::percentile('amount', 25), 'q1_amount')
            ->eval(QueryBuilder::percentile('amount', 75), 'q3_amount')
            ->eval(QueryBuilder::percentile('amount', 95), 'p95_amount')
            ->eval(QueryBuilder::medianAbsoluteDeviation('amount'), 'amount_mad')
            ->eval(QueryBuilder::countDistinct('customer_id'), 'unique_customers')
            ->eval(QueryBuilder::top('amount', 5, 'desc'), 'top_5_sales')
            ->eval(QueryBuilder::values('sales_channel'), 'channels_used')
            ->eval(QueryBuilder::weightedAvg('amount', 'quantity'), 'weighted_avg_amount')
            ->orderBy('total_revenue', 'DESC');

        $expected = "FROM sales_data\n" .
                   "| WHERE date >= '2024-01-01'\n" .
                   "| GROUP BY region, product_category\n" .
                   "| EVAL total_sales = count(*)\n" .
                   "| EVAL total_revenue = sum(amount)\n" .
                   "| EVAL avg_sale_amount = avg(amount)\n" .
                   "| EVAL median_sale_amount = median(amount)\n" .
                   "| EVAL amount_stddev = std_dev(amount)\n" .
                   "| EVAL q1_amount = percentile(amount, 25)\n" .
                   "| EVAL q3_amount = percentile(amount, 75)\n" .
                   "| EVAL p95_amount = percentile(amount, 95)\n" .
                   "| EVAL amount_mad = median_absolute_deviation(amount)\n" .
                   "| EVAL unique_customers = count_distinct(customer_id)\n" .
                   "| EVAL top_5_sales = top(amount, 5, 'desc')\n" .
                   "| EVAL channels_used = values(sales_channel)\n" .
                   "| EVAL weighted_avg_amount = weighted_avg(amount, quantity)\n" .
                   "| SORT total_revenue DESC";

        $this->assertEquals($expected, $query->toEsql());
    }
}
