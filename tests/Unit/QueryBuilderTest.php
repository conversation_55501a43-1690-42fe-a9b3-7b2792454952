<?php

declare(strict_types=1);

namespace EsqlPhp\Tests\Unit;

use EsqlPhp\QueryBuilder\QueryBuilder;
use PHPUnit\Framework\TestCase;

class QueryBuilderTest extends TestCase
{
    public function testSimpleQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('users')
            ->where('age', '>', 25)
            ->where('status', '=', 'active')
            ->orderBy('lastname', 'ASC')
            ->limit(10);

        $expected = "FROM users" . PHP_EOL .
                   "| WHERE age > 25 AND status = 'active'" . PHP_EOL .
                   "| SORT lastname ASC" . PHP_EOL .
                   "| LIMIT 10";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testAggregationQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('employees')
            ->groupBy(['department'])
            ->eval(QueryBuilder::avg('salary'), 'avg_salary')
            ->eval(QueryBuilder::count('*'), 'employee_count')
            ->where('hire_date', '>', '2020-01-01')
            ->orderBy('avg_salary', 'DESC');

        $expected = "FROM employees" . PHP_EOL .
                   "| WHERE hire_date > '2020-01-01'" . PHP_EOL .
                   "| GROUP BY department" . PHP_EOL .
                   "| EVAL avg(salary) AS avg_salary, count(*) AS employee_count" . PHP_EOL .
                   "| SORT avg_salary DESC";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testDataManipulationQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('logs')
            ->eval(QueryBuilder::dateFormat('timestamp', 'yyyy-MM-dd'), 'date')
            ->eval(QueryBuilder::concat(['user', '@', 'domain']), 'email')
            ->where('level', '=', 'ERROR')
            ->drop(['raw_message'])
            ->rename(['timestamp' => 'original_timestamp'])
            ->limit(20);

        $expected = "FROM logs" . PHP_EOL .
                   "| WHERE level = 'ERROR'" . PHP_EOL .
                   "| EVAL date_format(timestamp, 'yyyy-MM-dd') AS date, concat(user, '@', domain) AS email" . PHP_EOL .
                   "| DROP raw_message" . PHP_EOL .
                   "| RENAME timestamp TO original_timestamp" . PHP_EOL .
                   "| LIMIT 20";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testAdvancedTransformationsQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('products')
            ->eval(QueryBuilder::round('price * 1.2', 2), 'price_with_tax')
            ->where('stock', '>', 0)
            ->dissolve(['tags'])
            ->melt(['us_price', 'eu_price', 'asia_price'], 'region', 'regional_price')
            ->orderBy('name')
            ->keep(['product_id', 'name', 'category', 'price_with_tax', 'region', 'regional_price']);

        $expected = "FROM products" . PHP_EOL .
                   "| WHERE stock > 0" . PHP_EOL .
                   "| EVAL round(price * 1.2, 2) AS price_with_tax" . PHP_EOL .
                   "| KEEP product_id, name, category, price_with_tax, region, regional_price" . PHP_EOL .
                   "| DISSOLVE tags" . PHP_EOL .
                   "| MELT us_price, eu_price, asia_price AS region VALUES regional_price" . PHP_EOL .
                   "| SORT name ASC";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testComposedConditionsQuery(): void
    {
        $query = (new QueryBuilder())
            ->from('orders')
            ->where('status', '=', 'PENDING')
            ->where('total', '>', 100)
            ->orWhere('customer_id', 'IN', [1001, 1002, 1003])
            ->orderBy('total', 'DESC')
            ->limit(5);

        $expected = "FROM orders" . PHP_EOL .
                   "| WHERE status = 'PENDING' AND total > 100 OR customer_id IN [1001, 1002, 1003]" . PHP_EOL .
                   "| SORT total DESC" . PHP_EOL .
                   "| LIMIT 5";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testDocumentationExample(): void
    {
        $query = (new QueryBuilder())
            ->from('kibana_sample_data_ecommerce')
            ->where('products.category', '=', "Men's Clothing")
            ->dissolve(['products'])
            ->where('products.category', '=', "Men's Clothing")
            ->keep(['order_id', 'customer_id', 'products.product_id', 'products.category']);

        $expected = "FROM kibana_sample_data_ecommerce" . PHP_EOL .
                   "| WHERE products.category = 'Men''s Clothing' AND products.category = 'Men''s Clothing'" . PHP_EOL .
                   "| KEEP order_id, customer_id, products.product_id, products.category" . PHP_EOL .
                   "| DISSOLVE products";

        $this->assertEquals($expected, $query->toEsql());
    }

    public function testMathFunctionsStatic(): void
    {
        $abs = QueryBuilder::abs(-10);
        $ceil = QueryBuilder::ceil(10.2);
        $floor = QueryBuilder::floor(10.8);
        $round = QueryBuilder::round(10.567, 2);

        $this->assertEquals("abs(-10)", $abs->toString());
        $this->assertEquals("ceil(10.2)", $ceil->toString());
        $this->assertEquals("floor(10.8)", $floor->toString());
        $this->assertEquals("round(10.567, 2)", $round->toString());
    }

    public function testStringFunctionsStatic(): void
    {
        $concat = QueryBuilder::concat(["'Hello'", "'World'"]);
        $length = QueryBuilder::length("'Hello'");
        $substring = QueryBuilder::substring("'Hello'", 1, 3);
        $lowercase = QueryBuilder::lowercase("'HELLO'");
        $uppercase = QueryBuilder::uppercase("'hello'");

        $this->assertEquals("concat('Hello', 'World')", $concat->toString());
        $this->assertEquals("length('Hello')", $length->toString());
        $this->assertEquals("substring('Hello', 1, 3)", $substring->toString());
        $this->assertEquals("lowercase('HELLO')", $lowercase->toString());
        $this->assertEquals("uppercase('hello')", $uppercase->toString());
    }

    public function testDateFunctionsStatic(): void
    {
        $now = QueryBuilder::now();
        $dateFormat = QueryBuilder::dateFormat('timestamp', 'yyyy-MM-dd');
        $dateTrunc = QueryBuilder::dateTrunc('month', 'timestamp');

        $this->assertEquals("now()", $now->toString());
        $this->assertEquals("date_format(timestamp, 'yyyy-MM-dd')", $dateFormat->toString());
        $this->assertEquals("date_trunc('month', timestamp)", $dateTrunc->toString());
    }

    public function testAggregationFunctionsStatic(): void
    {
        $count = QueryBuilder::count();
        $sum = QueryBuilder::sum('price');
        $avg = QueryBuilder::avg('age');
        $min = QueryBuilder::min('price');
        $max = QueryBuilder::max('price');

        $this->assertEquals("count(*)", $count->toString());
        $this->assertEquals("sum(price)", $sum->toString());
        $this->assertEquals("avg(age)", $avg->toString());
        $this->assertEquals("min(price)", $min->toString());
        $this->assertEquals("max(price)", $max->toString());
    }
}
